{"name": "@ali/b2b-finance-web-byf-merchant-uniapp", "version": "1.0.0", "description": "ice.js uniapp web scaffold", "dependencies": {"@ali/iec-dtao-utils": "0.0.8", "@ali/picture": "^2.5.3", "@ali/wormhole-context": "^1.0.0", "@alife/dtao-iec-spm-log": "^0.0.13", "@ice/runtime": "^1.0.0", "antd-mobile": "^5.37.1", "antd-mobile-icons": "^0.3.0", "dayjs": "^1.11.13", "lodash-es": "^4.17.21", "numeral": "^2.0.6", "react": "^18.2.0", "react-dom": "^18.2.0"}, "devDependencies": {"@ali/build-plugin-pegasus-project": "^2.0.0", "@ali/eslint-config-att": "^1.0.6", "@ali/eslint-config-fin": "^0.0.16", "@ali/fin-lint": "^1.1.11", "@ali/ice-plugin-def": "^1.2.4", "@ali/ice-plugin-spm": "^3.0.1", "@ali/ice-plugin-uniapp": "^0.1.0", "@applint/spec": "^1.2.3", "@ice/app": "^3.0.0", "@ice/plugin-pha": "^3.0.4", "@types/node": "^18.11.17", "@types/react": "^18.0.0", "@types/react-dom": "^18.0.0", "eslint": "^6.8.0", "husky": "^0.14.3", "stylelint": "^14.16.1", "typescript": "^4.9.5"}, "scripts": {"start": "ice start", "build": "ice build", "eslint": "eslint ./src --cache --ext .js,.jsx,.ts,.tsx", "eslint:fix": "npm run eslint -- --fix", "stylelint": "stylelint \"src/**/*.{css,scss,less}\" --cache", "stylelint:fix": "npm run stylelint -- --fix", "lint": "mad lint", "precommit": "mad lint --staged", "postcommit": "mad lint --postcommit"}, "publishConfig": {"registry": "http://registry.npm.alibaba-inc.com"}, "repository": "**************************:b2b-finance-web/byf-merchant-uniapp.git"}