import { defineConfig } from '@ice/app';
import { PegasusPlugin } from '@ali/build-plugin-pegasus-project';
import spm from '@ali/ice-plugin-spm';
import uniapp from '@ali/ice-plugin-uniapp';

// The project config, see https://ice3.alibaba-inc.com/v3/docs/guide/basic/config
export default defineConfig({
  // Set your configs here.
  plugins: [
    PegasusPlugin({
      previewMode: 'local',
      documentOnly: true,
      npm: false,
    }),
    uniapp(),
    spm(),
  ],
  server: {
    // Wormhole only support server bundle with cjs format.
    format: 'cjs',
    ignores: [
      {
        resourceRegExp: /^uuid$/,
      },
      {
        resourceRegExp: /^axios$/,
      },
      {
        resourceRegExp: /^@ali\/iec-dtao-utils\/esm\/common\/number$/,
      },
    ],
    bundle: true,
  },
  codeSplitting: 'page',
  ssg: false,
  ssr: false,
});
