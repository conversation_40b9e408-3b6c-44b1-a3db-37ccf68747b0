export const getCookie = () => {
  try {
    const { cookie } = document;
    let match = cookie && cookie.match(/(?:^|;)\s*xman_us_t\s*=\s*([^;]+)/);
    if (match) {
      match = match[1].match(/(?:^|&)\s*ctoken\s*=\s*([^&]+)/);
    }
    const ctoken = match && match[1];
    // 支持tbtoken
    match = cookie && cookie.match(/(?:^|;)\s*_tb_token_\s*=\s*([^;]+)/);
    const tb_token = match && match[1];
    return ctoken || tb_token || '';
  } catch (e) {
    return '';
  }
};
