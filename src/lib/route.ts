import { queryMerchantStatus } from '@/api/query';

export const buildUrl = (newLastSegment: string) => {
  // 创建一个 URL 对象
  const url = new URL(window.location.href);

  // 分割路径
  const pathSegments = url.pathname.split('/').filter((segment) => segment !== '');

  // 替换最后一个路径段
  if (pathSegments.length > 0) {
    pathSegments[pathSegments.length - 1] = newLastSegment;
  } else {
    // 如果路径为空，直接添加新的段
    pathSegments.push(newLastSegment);
  }

  // 重建路径
  url.pathname = pathSegments.join('/');

  return url.href;
};

export function replace(newLastSegment) {
  const url = buildUrl(newLastSegment);

  // 更新 location.href
  window.location.href = url;
}

export const routeByUserStatus = async () => {
  try {
    const statusData = await queryMerchantStatus();
    switch (statusData?.status) {
      case 'null':
      case 'MERCHANT_OPENING':
      case 'MERCHANT_OPEN_FAILED':
      case 'MERCHANT_QUIT_SUCCEED':
        replace('/open');
        return;
      case 'MERCHANT_OPEN_SUCCEED':
      case 'MERCHANT_FROZEN':
      case 'MERCHANT_QUITTING':
        replace('/detail');
        return;
      default:
        replace('/open');
        break;
    }
  } catch (error) {
    replace('/open');
  }
};
