import { Toast } from 'antd-mobile';
import { CheckCircleFill, CloseCircleFill } from 'antd-mobile-icons';

export const errorToast = (message?: string) => {
  Toast.clear();
  Toast.show({
    icon: <CloseCircleFill />,
    content: message || '请求失败，请稍后再试！',
  });
};

interface SuccessToastOptions {
  duration?: number;
  afterClose?: () => void;
  maskClickable?: boolean;
}

export const successToast = (message?: string, options?: SuccessToastOptions) => {
  const { duration, afterClose, maskClickable = true } = options || {};
  Toast.clear();
  Toast.show({
    icon: <CheckCircleFill />,
    content: message || '操作成功！',
    duration,
    afterClose,
    maskClickable,
  });
};


export const loadingToast = (message?: string) => {
  Toast.clear();
  Toast.show({
    icon: 'loading',
    content: message || '加载中…',
  })
}