/**
 * @file 获取umidToken
 */

import { log } from '@alife/dtao-iec-spm-log';

export interface UmidTokenRes {
  ret?: 'HY_SUCCESS';
  status?: 'SUCCESS';
  aluUmid?: string;
}

export function getUmidToken(): Promise<UmidTokenRes> {
  return new Promise<UmidTokenRes>((resolve) => {
    try {
      if (!window?.WindVane?.call) {
        log.addOtherLog('ALU_WVJSBridge_GET_UMID_FAIL');
        return resolve({});
      }

      window.WindVane.call(
        'aluWVJSBridge',
        'getUmid',
        {},
        (res: UmidTokenRes) => {
          if (res?.aluUmid && (res?.ret === 'HY_SUCCESS' || res?.status === 'SUCCESS')) {
            resolve(res);
          } else {
            log.addOtherLog('ALU_WVJSBridge_GET_UMID_FAIL');
            resolve({});
          }
        },
        () => {
          log.addOtherLog('ALU_WVJSBridge_GET_UMID_FAIL');
          resolve({});
        },
      );
    } catch (e) {
      resolve({});
    }
  });
}
