import { get, defaultTo } from 'lodash-es';
import dayjs from "dayjs";

interface FatigueParams {
  itemId: string;
  bizType: string;
  fatigue: string;
}
export const fatigueJudgement = (fatigueInfo: FatigueParams, positionCode: string) => {
  // 获取第一个元素
  // 获取疲劳度设置
  const fatitueStr = get(fatigueInfo, 'fatigue');
  // 获取活动项id
  const itemId = get(fatigueInfo, 'itemId');
  // 获取track
  const fatigue = fatitueStr ? JSON.parse(fatitueStr) : null;
  // 周期个数
  const periodAmount = defaultTo(Number(get(fatigue, 'periodAmount')), 1);
  // 周期
  const frequency = defaultTo(get(fatigue, 'frequency'), 'years');
  // 周期内疲劳度
  const times = defaultTo(Number(get(fatigue, 'times')), 1);
  // 总疲劳度
  const totalTimes = defaultTo(Number(get(fatigue, 'totalTimes')), 1);
  // 物料类型
  const materialType = get(fatigueInfo, 'bizType');
  try {
    // localStorage的key
    const localStorageKey = `${positionCode}-${itemId}-${materialType}`;
    const fatigueConfigStr = localStorage.getItem(localStorageKey);
    if (!fatigueConfigStr) {
      // 如果没有设置过疲劳度缓存,说明第一次来
      // 获取周期到期的时间（自然日）
      const periodEnd = dayjs().add(periodAmount, frequency).startOf(frequency).valueOf();
      // 周期内疲劳度
      const currentPeriodCount = times - 1;
      // 总疲劳度
      const totalCount = totalTimes - 1;
      // 设置缓存
      localStorage.setItem(localStorageKey, JSON.stringify({ periodEnd, currentPeriodCount, totalCount }));
      // 设置为可见
      return true;
    } else {
      // 如果设置过疲劳度
      const fatigueConfig = JSON.parse(fatigueConfigStr);
      const { periodEnd, currentPeriodCount, totalCount } = fatigueConfig;

      if (dayjs().valueOf() < periodEnd) {
        // 在当前周期内
        if (currentPeriodCount > 0 && totalCount > 0) {
          // 如果周期内还有次数切总疲劳度还有次数，周期内有效次数-1，总疲劳度-1
          localStorage.setItem(localStorageKey, JSON.stringify({ periodEnd, currentPeriodCount: currentPeriodCount - 1, totalCount: totalCount - 1 }));
          return true;
        } else {
          return false;
        }
      } else if (totalCount > 0) {
        // 如果已经不在上一周期内，重新开启一个新周期
        // 如果还有总疲劳度剩余次数
        // 获取周期到期的时间
        const newPeriodEnd = dayjs().add(periodAmount, frequency).startOf(frequency).valueOf();
        // 设置缓存
        localStorage.setItem(localStorageKey, JSON.stringify({ periodEnd: newPeriodEnd, currentPeriodCount: times - 1, totalCount: totalCount - 1 }));
        // 设置为可见
        return true;
      } else {
        return false;
      }
    }
  } catch (error) {
    return false;
  }
};