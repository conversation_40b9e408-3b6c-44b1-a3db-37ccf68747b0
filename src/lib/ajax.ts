/**
 * @file 对请求方法的再包装
 */

import { ajax } from '@ali/iec-dtao-utils';

interface RequestOptions<D> {
  url: string;
  data?: D;
}

function getBaseParams<D>(options: RequestOptions<D>) {
  return {
    ...options,
    extraConfig: {
      headers: {
        // 项目预发的隔离环境标也在这里
        //'Eagleeye-Userdata': 'dpath_env=20250701-pre-shgyxm',
        //'X-Biz-Info': 'mc-sys-aenv=20250701-pre-shgyxm',
        appRequestSign: 'FUI_UTIL_APP_MINIAPP_REQUEST',
      },
    },
  };
}

export function get<D, T>(options: RequestOptions<D>) {
  return ajax.get<D, T>(getBaseParams(options));
}

export function post<D, T>(options: RequestOptions<D>) {
  return ajax.post<D, T>(getBaseParams(options));
}
