import { NoticeBarOptions } from '@/components/NoticeBar';
import { StyleConfig, PriceTypeConfig } from '@/types';

export const PRICE_TITLE = '预估服务费';
export const PRICE_DESC =
  '当前服务费为预估服务费，费用按单收取，我们将根据您店铺交易订单及退换货情況每日动态调整服务费，最终服务费以出单为准。';

export const ORIGINAL_PRICE_TITLE = '专项优惠';
export const ORIGINAL_PRICE_DESC = '该价格为优惠前的价格';

export const ERROR_TITLE = '开通失败';
export const ERROR_DESC = '您的支付宝账号认证等级不足，请先到支付宝完成认证后再开通';

export const EXIT_ERROR_TITLE = '暂无法退出';
export const EXIT_ERROR_DESC = '对不起，你已经参加了承诺参与固定天数的活动，活动有效期内无法退出';

// export const BASE_URL = 'https://b2bfin.taobao.com';

// 预发环境
export const BASE_URL = 'https://pre-1b2bfin.taobao.com';

export const PRODUCT_CODE = 'BYF';

export const OPEN_FAILED_CODE = {
  USER_HAS_BLOCK: '您的支付宝账号被冻结，请先到支付宝解冻后再开通',
  ACCOUNT_LEVEL_CHECK_FAIL: '您的支付宝账号认证等级不足，请先到支付宝完成认证后再开通',
  STATUS_NO_CERT: '您的支付宝账号暂未实名，请先到支付宝完成实名后再开通',
  LOGONID_IS_REPEATED: '您的支付宝账号与其他人重复，暂无法成功开通，如有疑问请联系客服',
  ADMIT_REJECT: '很抱歉，您暂不满足退货宝的开通条件，如有疑问请联系客服',
  DEFAULT_FRONT_ERROR: '开通失败，请联系客服',
};

export const QUIT_TYPE = 'SELLER_QUIT';
export const TRADE_ACCOUNT = 'TRADE_ACCOUNT';

export const MOBILE_OPEN_CHANNEL = 'qianniu_h5';

export const STATUS_INFO = {
  undefined: {
    title: '暂未开通',
    color: '#31CC31',
    bgColor: 'rgba(49, 204, 49, 0.06)',
  },
  null: {
    title: '暂未开通',
    color: '#31CC31',
    bgColor: 'rgba(49, 204, 49, 0.06)',
  },
  MERCHANT_OPENING: {
    title: '开通中',
    color: '#31CC31',
    bgColor: 'rgba(49, 204, 49, 0.06)',
  },
  MERCHANT_OPEN_SUCCEED: {
    title: '保障中',
    color: '#31CC31',
    bgColor: 'rgba(49, 204, 49, 0.06)',
  },
  MERCHANT_OPEN_FAILED: {
    title: '暂未开通',
    color: '#31CC31',
    bgColor: 'rgba(49, 204, 49, 0.06)',
  },
  MERCHANT_FROZEN: {
    title: '已冻结',
    color: '#FF0000',
    bgColor: 'rgba(255, 0, 0, 0.06)',
  },
  MERCHANT_QUITTING: {
    title: '退出中',
    color: '#FF8000',
    bgColor: 'rgba(255, 128, 0, 0.06)',
  },
  MERCHANT_QUIT_SUCCEED: {
    title: '暂未开通',
    color: '#31CC31',
    bgColor: 'rgba(49, 204, 49, 0.06)',
  },
};

export const STATUS_TIPS_INFO = {
  MERCHANT_FROZEN: {
    type: "error",
    content: '系统识别您的店铺异常，暂时冻结退货宝服务，为此给您带来的不便敬请谅解。请保障店铺正常经营，如后续异常解除，平台会自动解除冻结',
  },
  MERCHANT_FROZEN_ARREARS: {
    type: "error",
    content: '您的店铺已欠费，暂时关闭退货宝服务，请尽快向服务费扣款支付宝账户充值，充值后系统将定期从您的账户中自动划扣，扣费成功将自动解除冻结',
  },
  MERCHANT_FROZEN_OTHER: {
    type: "error",
    content: '系统识别您的店铺异常，暂时冻结退货宝服务，为此给您带来的不便敬请谅解。请保障店铺正常经营，如后续异常解除，平台会自动解除冻结',
  },
  MERCHANT_QUITTING: {
    type: "alert",
    content: '服务退出中，请耐心等待。',
  },
};

export const PROMOTION_APPLY_BTN = '双11期间开通立享前1111单5折优惠';

export const ByfOpenBtnMarketingTextApp = 'byfOpenBtnMarketingTextApp';
export const ByfHomeBtnMarketingTextApp = 'byfHomeBtnMarketingTextApp';
export const ByfAppHomePoplayer = 'byfAppHomePoplayer';
export const ByfAppOpenPoplayer = 'byfAppOpenPoplayer';

export const PartSettingText = {
  'null': '全店开通',
  PART: '部分开通',
  ALL: '全店开通',
}

export const CLOSE_ICON = 'https://gw.alicdn.com/imgextra/i1/O1CN01xBvzLY1DlIQYnnalp_!!6000000000256-2-tps-144-144.png';

export const WARNING_IMG = 'https://gw.alicdn.com/imgextra/i2/O1CN01nNZ4jr21KwPY9cs5z_!!6000000006967-2-tps-48-48.png';

export const QIANNIU_H5 = 'qianniu_h5';

export const RED_ENVELOPE_DESC = "领取成功后可到PC端「退货宝-我的权益」查看";

export const QUIT_REASON_LIST = [{
  value: '成交转化没有提升',
  label: '成交转化没有提升',
  desc: '含退货宝服务的商品，平均订单成交转化提升了约25%。',
}, {
  value: '店铺纠纷下降不及预期',
  label: '店铺纠纷下降不及预期',
  desc: '含退货宝服务订单的投诉率降低了约40%，避免运费纠纷。',
},
{
  value: '服务费太高，支出成本大',
  label: '服务费太高，支出成本大',
  desc: '服务费和您的退货率相关，您接受的服务费价格是:',
  isInput: true,

},
{
  value: '赔付金额过高，消费者恶意骗补',
  label: '赔付金额过高，消费者恶意骗补',
  desc: '我们正在持续治理买家恶意骗补行为，若您发现此情况，您可以通过万象客服进行反馈，我们将第一时间核实处理。',
},
{
  value: '赔付金额过低，买家要求店铺补运费差价',
  label: '赔付金额过低，买家要求店铺补运费差价',
},
{
  value: '商品退货率明显上升',
  label: '商品退货率明显上升',
},
{
  value: '其他',
  label: '其他',
  isInput: true,
}];

export const INTEREST_LIST = [{
  desc: '流量曝光',
  exposure: 26,
  path: 'https://gw.alicdn.com/imgextra/i3/O1CN01NZnzp11IdZqSMr3W0_!!6000000000916-2-tps-253-168.png',
},
{
  desc: '专属标识促进下单转化',
  exposure: 20,
  path: 'https://gw.alicdn.com/imgextra/i3/O1CN01P3IYyL1ioH6cXP12T_!!6000000004459-2-tps-253-168.png',
}];

export const RECEIVE_RESULT_SUCCESS_INFO = {
  type: 'success',
  title: '领取成功',
  desc: "恭喜，您的权益已到账，保障持续不断档，退货宝将努力为您提供更好的服务",
  code: 'success',
  okText: '我知道了',
  hasCancel: false,
}

export const RECEIVE_RESULT_ERROR_INFO = {
  "ERROR-2001": {
    type: 'error',
    title: '领取失败',
    desc: "抱歉，您已经参与过此活动，暂时无法领取，您是否仍选择退出？",
    code: 'ERROR-2001',
    hasCancel: true,
  },
  "ERROR-4017": {
    type: 'error',
    title: '领取失败',
    desc: "系统异常，暂时无法参与当前活动，请在后续保持关注，您是否仍选择退出？",
    code: 'ERROR-4017',
    hasCancel: true,
  },
  "default": {
    type: 'error',
    title: '领取失败',
    desc: "系统异常，暂时无法参与当前活动，您是否仍选择退出？",
    code: 'default',
    hasCancel: true,
  }
}

export const ICON_MAP = {
  alert:
    'https://gw.alicdn.com/imgextra/i3/O1CN010LbLZe1ji9NHtjSNP_!!6000000004581-2-tps-120-120.png',
  error:
    'https://gw.alicdn.com/imgextra/i1/O1CN01GMOtpJ1OM4Lhrlumy_!!6000000001690-2-tps-120-120.png',
  info:
    'https://gw.alicdn.com/imgextra/i1/O1CN01IOdqvW28vJD4NuhfC_!!6000000007994-2-tps-56-56.png',
  default: 
    'https://gw.alicdn.com/imgextra/i2/O1CN016FT71D1WDqDmhudog_!!6000000002755-2-tps-56-56.png',
};

export const PROTOCOL_URL =
  'https://terms.alicdn.com/legal-agreement/terms/b_end_product_protocol/20240731161354834/20240731161354834.html';


export const COUPON_CHECK_ICON = 'https://gw.alicdn.com/imgextra/i1/O1CN01QQbTbX1TanERXTlII_!!6000000002399-2-tps-56-56.png';

export const COUPON_CAR_ICON = 'https://gw.alicdn.com/imgextra/i4/O1CN01Ncpyut1e59e8bQ4TY_!!6000000003819-2-tps-520-520.png';

export const COUPON_TIPS_ICON = 'https://gw.alicdn.com/imgextra/i1/O1CN01rKI5t61qVyDvOJ99P_!!6000000005502-2-tps-240-107.png';

export const COUPON_HEADER_IMG = 'https://gw.alicdn.com/imgextra/i1/O1CN01cG60pw1E44hdPtV9u_!!6000000000297-2-tps-864-196.png';

export const ACTIVITY_COUNT = 5;
/**
 * 年框样式配置 - 橙色主题
 */
export const YEAR_STYLE: StyleConfig = {
  themeColor: '#c45902',
  gradientFrom: '#fff7ed',
  gradientTo: '#fff0f0',
  selectedBorderColor: '#c45902',
  noticeBackground: 'linear-gradient(180deg, #fff8f3 0%, #ffece5 100%)',
  noticeSelectedBackground: 'linear-gradient(180deg, #ffe6d2 0%, #ffcab6 100%)',
  arrowBackground: '#ffcdb8',
  buttonBackground: 'linear-gradient(180deg, #FFD2AE 0%, #FFB99D 100%)',
  manageNoticeBackground:'#ffe6d2'
};

/**
 * 月框样式配置 - 橙色主题（目前与年框保持一致）
 */
export const MONTH_STYLE: StyleConfig = {
  themeColor: '#c45902',
  gradientFrom: '#fff7ed',
  gradientTo: '#fff0f0',
  selectedBorderColor: '#c45902',
  noticeBackground: 'linear-gradient(180deg, #fff8f3 0%, #ffece5 100%)',
  noticeSelectedBackground: 'linear-gradient(180deg, #ffe6d2 0%, #ffcab6 100%)',
  arrowBackground: '#ffcdb8',
  buttonBackground: 'linear-gradient(180deg, #FFD2AE 0%, #FFB99D 100%)',
  manageNoticeBackground:'#ffe6d2'
};

/**
 * 日常价样式配置 - 蓝色主题
 */
export const BASE_STYLE: StyleConfig = {
  themeColor: '#3d5eff',
  gradientFrom: 'rgba(61, 94, 255, 0.15)',
  gradientTo: 'rgba(61, 94, 255, 0)',
  selectedBorderColor: '#3d5eff',
  noticeBackground: '#f0f2fa',
  noticeSelectedBackground: '#f0f2fa',
  arrowBackground: '#f0f2fa',
  buttonBackground: 'linear-gradient(135deg, #3d5eff 0%, #1e40af 100%)',
};

// 当前启用的固定价类型
type EnabledFixedPrice = 'YEAR' | 'MONTH';

/**
 * 固定价类型配置映射
 * 包含所有固定价类型的配置信息
 */
export const FIXED_PRICE_CONFIG: Record<EnabledFixedPrice, PriceTypeConfig> = {
  YEAR: {
    title: '年框一口价',
    privilegeText: '专属特权，365天固定价格',
    days: 365,
    detailText: '该活动是限时邀请制，只针对部分符合准入条件的商家开放，商家不能主动申请参与。',
    isFixed: true,
    styles: YEAR_STYLE,
  },
  MONTH: {
    title: '月框一口价',
    privilegeText: '专属特权，30天固定价格',
    days: 30,
    detailText: '该活动是限时邀请制，只针对部分符合准入条件的商家开放，商家不能主动申请参与。',
    isFixed: true,
    styles: MONTH_STYLE,
  },
};

/**
 * 日常价配置
 * 非固定价格的配置信息
 */
export const BASE_PRICE_CONFIG: PriceTypeConfig = {
  title: '今日服务费',
  privilegeText: '价格动态更新',
  days: 0,
  detailText: '服务费将根据店铺交易订单退换货等情况动态调整，最终以实际出单收费为准。',
  isFixed: false,
  styles: BASE_STYLE,
};

