import { PriceTypeConfig } from '@/types';
import { BASE_PRICE_CONFIG, FIXED_PRICE_CONFIG, YEAR_STYLE } from '@/lib/constant';

/**
 * 异步延迟
 * @param time - 延迟时间（毫秒）
 * @returns - 返回一个 Promise，当延迟结束后 resolve
 * 场景：多个弹框连续打开关闭时，弹框动画卡顿问题
 */
function animationDelay(time = 200) {
  return new Promise((resolve) => setTimeout(resolve, time));
}

/**
 * 获取价格展示配置
 * 根据优惠标志决定显示哪个价格和是否显示原价
 * @param feeAmount - 原价金额
 * @param discountFeeAmount - 优惠价金额
 * @param promotionFlag - 是否有优惠
 * @returns 价格展示配置对象
 */
function getPriceDisplay(
  feeAmount: string,
  discountFeeAmount?: string,
  promotionFlag?: boolean
) {
  return {
    // 当前显示价格：有优惠时显示优惠价，否则显示原价
    currentPrice: promotionFlag ? discountFeeAmount : feeAmount,
    // 原价（用于划线显示）
    originalPrice: feeAmount,
    // 是否显示原价：只有在有优惠时才显示
    showOriginalPrice: promotionFlag,
  };
}

/**
 * 获取价格类型配置
 * @param type - 价格类型：'base' | 'fixed'
 * @param fixedPriceType - 固定价类型（当 type 为 'fixed' 时需要）
 * @returns 价格类型配置对象
 */
function getPriceTypeConfig(
  type: 'base' | 'fixed',
  fixedPriceType?: string
): PriceTypeConfig {
  // 日常价配置
  if (type === 'base') {
    return BASE_PRICE_CONFIG;
  }
  
  // 固定价配置 从键值对里找
  if (type === 'fixed' && fixedPriceType) {
    return FIXED_PRICE_CONFIG[fixedPriceType];
  }
  
  // 默认固定价配置（用于未知的固定价类型）
  return {
    title: '固定价',
    privilegeText: '固定价格',
    days: 0,
    detailText: '该活动是限时邀请制，只针对部分符合准入条件的商家开放，商家不能主动申请参与。',
    isFixed: true,
    styles: YEAR_STYLE, // 默认使用年框样式
  };
}

/**
 * 生成动态CSS变量样式
 * 将配置中的样式转换为CSS变量，供组件使用
 * @param config - 价格类型配置
 * @returns CSS变量样式对象
 */
function generateDynamicStyles(config: PriceTypeConfig) {
  const { styles } = config;
  return {
    '--theme-color': styles.themeColor,
    '--gradient-from': styles.gradientFrom,
    '--gradient-to': styles.gradientTo,
    '--selected-border-color': styles.selectedBorderColor,
    '--notice-background': styles.noticeBackground,
    '--notice-selected-background': styles.noticeSelectedBackground,
    '--arrow-background': styles.arrowBackground,
    '--button-background': styles.buttonBackground,
    '--privilege-tag-background':styles.manageNoticeBackground
  } as React.CSSProperties;
}

export {
  animationDelay,
  getPriceDisplay,
  getPriceTypeConfig,
  generateDynamicStyles,
};