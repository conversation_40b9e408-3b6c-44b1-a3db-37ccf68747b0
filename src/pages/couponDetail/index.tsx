import { definePageConfig } from 'ice';
import { useEffect, useState } from 'react';
import { getUrlParams } from '../../lib/params';
import styles from './index.module.css';

const CouponDetail = () => {
  const [couponId, setCouponId] = useState<string>('');
  const [couponData, setCouponData] = useState<any>(null);

  useEffect(() => {
    // 从URL参数中获取优惠券ID
    const id = getUrlParams(window.location.href, 'id');
    console.log('接收到的优惠券ID:', id);
      
  }, []);


  return (
    <div className={styles.couponDetail}>
        <h1>优惠券详情</h1>
      </div>

  );
};

export default CouponDetail;
export const pageConfig = definePageConfig(() => ({
  title: '权益详情',
  spm: {
    spmB: 'BYF_MERCHANT_UNIAPP-couponDetail',
  },
}));
