/**
 * @file 常见问题页面
 */

import { definePageConfig } from 'ice';

import Question from '@/components/Question';
import { useEffect } from 'react';
import { log } from '@alife/dtao-iec-spm-log';
import { handleAemLoad } from '@/lib/ameLog';

const QA = () => {
  useEffect(() => {
    handleAemLoad();
    log.reportInit('a360n.BYF_MERCHANT_UNIAPP-qa');
  }, []);

  return (
    <div style={{ padding: '0 32rpx', background: 'white' }}>
      <Question type="showAll" />
    </div>
  );
};

export const pageConfig = definePageConfig(() => ({
  title: '常见问题',
}));

export default QA;

