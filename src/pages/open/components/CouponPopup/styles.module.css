
.coupon-popup-container {
  padding: 0 32rpx 32rpx;
}

/* 标题 */
.coupon-popup-title {
  font-size: 32rpx;
  font-weight: 500;
  line-height: 48px;
  text-align: center;
  color: #111;
}
.coupon-popup-content {
  padding-top: 40rpx;
  padding-bottom: 38rpx;
}
.coupon-popup-header {
  margin-bottom: 40rpx;
}
.coupon-popup-header > img {
  width: 429rpx;
}
.title-text {
  font-family: DingTalk JinBuTi;
  font-size: 48rpx;
  line-height: 1;
  margin-bottom: 12rpx;
  color: #111;
}
.coupon-item-desc {
  font-family: DingTalk JinBuTi;
  display: flex;
  align-items: center;
  font-size: 26rpx;
  color: #111;
}
.coupon-item-desc > img {
  width: 28rpx;
  height: 28rpx;
  margin-right: 8rpx;
}

/* 优惠券列表 */
.coupon-list {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}
.coupon-item {
  position: relative;
  padding: 32rpx;
  gap: 8rpx;
  border-radius: 12rpx;
}
.coupon-item:nth-child(even) {
  background: linear-gradient(270deg, #f2a95e 0%, #ffc890 100%);
}
.coupon-item:nth-child(odd) {
  background: #452ee5;
}
.coupon-item-name {
  font-size: 36rpx;
  font-weight: 500;
  line-height: 52rpx;
  color: #fff;
  text-align: center;
}
.coupon-item-amount {
  text-align: center;
}
.coupon-item-amount .estimate {
  font-size: 24rpx;
  text-align: center;
  color: #fff;
}
.coupon-item-amount .discount-amount {
  font-size: 24rpx;
  text-align: center;
  color: #fff;
  margin: 0 4rpx;
}
.coupon-item-amount .origin-amount {
  text-align: center;
  text-decoration: line-through;
  color: rgba(255, 255, 255, 0.5);
  font-size: 26rpx;
  margin-right: 4rpx;
}
.coupon-banner-icon {
  position: absolute;
  left: 0;
  top: 0;
  background: url('https://gw.alicdn.com/imgextra/i1/O1CN01371AqY1Gn4qlNdjhP_!!6000000000666-2-tps-208-76.png')
    no-repeat;
  width: 104rpx;
  font-size: 24rpx;
  background-size: 100%;
  line-height: 30rpx;
  text-align: center;
  color: #fff;
}
.coupon-car-icon {
  position: absolute;
  right: 0;
  top: -260rpx;
  width: 260rpx;
  height: 260rpx;
}

/* 协议 */
.coupon-popup-agreement {
  display: flex;
  align-items: center;
  font-size: 26rpx;
  line-height: 40rpx;
  color: #111;
  padding: 16rpx 0;
}
.coupon-popup-agreement span {
  color: #3d5eff;
}
  

 { /* 按钮 */ }
.coupon-popup-footer {
  display: flex;
  justify-content: space-between;
  margin-bottom: 16rpx;
}
.coupon-popup-footer button {
  width: 49%;
}

/* 免责声明 */
.coupon-popup-footer-desc {
  font-size: 24rpx;
  color: #6666;
}


/* 协议 */
.agreement {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;

  iframe {
    border: none;
    width: 100%;
    min-height: 70vh;
  }

  button {
    margin: 16rpx;
    width: calc(100% - 32rpx);
  }
}
.title {
  font-size: 32rpx;
  font-weight: 500;
  line-height: 48rpx;
  color: var(--text-base);
  padding: 24rpx 0;
}
.backIcon {
  position: absolute;
  left: 24rpx;  
  top: 24rpx;
  width: 36rpx;
  height: 36rpx;
}