
import React, { memo, useState } from 'react';
import CustomPopup from '@/components/CustomPopup';
import CouponPopup from './CouponPopup';
import { log } from '@ali/iec-dtao-utils';
import { BasePopInfo, CouponPopInfo } from '@/types';

interface BaseMarketingPopProps {
  ugTrack: string;
  basePopInfo: BasePopInfo;
  couponPopInfo: CouponPopInfo;
  onReceive: () => void;
  setBasePopInfoFun: (params: any) => void;
  setCouponPopInfoFun: (params: any) => void;
}

const BaseCouponPopup = (props: BaseMarketingPopProps) => {
  const {
    ugTrack,
    basePopInfo,
    couponPopInfo,
    onReceive = () => { },
    setBasePopInfoFun = () => { },
    setCouponPopInfoFun = () => { },
  } = props;
  const { visible, imageUrl, actionUrl } = basePopInfo || {};
  const { couponPopVisible, couponList, effectiveDays, canNotQuitDays } = couponPopInfo || {};

  const [agreementPopVisible, setAgreementPopVisible] = useState<boolean>(false);

  return (
    <>
      {/* 基础弹窗 */}
      <CustomPopup
        visible={visible}
        imageUrl={imageUrl}
        onImgClick={() => {
          log.addLog('base-popup-img-click', 'click', { ugTrack });
          setBasePopInfoFun({ visible: false });
          window.open(actionUrl, '_blank');
        }}
        onClose={() => {
          log.addLog('base-popup-close-click', 'click', { ugTrack });
          setBasePopInfoFun({ visible: false });
        }}
        onMaskClick={() => {
          log.addLog('base-popup-Mask-click', 'click', { ugTrack });
          setBasePopInfoFun({ visible: false });
        }}
      />
      {/* 营销弹窗 */}
      <CouponPopup
        visible={couponPopVisible}
        couponList={couponList}
        effectiveDays={effectiveDays}
        canNotQuitDays={canNotQuitDays}
        agreementPopVisible={agreementPopVisible}
        onReceive={() => onReceive()}
        onAgreementClose={() => {
          log.addLog('agreement-popup-close', 'click', { ugTrack });
          setAgreementPopVisible(false);
        }}
        onAgreement={() => {
          log.addLog('agreement-click', 'click', { ugTrack });
          setAgreementPopVisible(true);
        }}
        onCouponClose={() => {
          log.addLog('coupon-popup-close', 'click', { ugTrack });
          setCouponPopInfoFun({ couponPopVisible: false });
        }}
        onCancel={() => {
          log.addLog('coupon-popup-cancel', 'click', { ugTrack });
          setCouponPopInfoFun({ couponPopVisible: false });
        }}
        onAgreementBack={() => {
          log.addLog('agreement-popup-close', 'click', { ugTrack });
          setAgreementPopVisible(false);
        }}
      />
    </>
  );
};

export default memo(BaseCouponPopup);
