
import { map } from 'lodash-es';
import { COUPON_CAR_ICON, COUPON_CHECK_ICON, COUPON_HEADER_IMG, PROTOCOL_URL } from '@/lib/constant';
import { Button, Popover, Popup, SafeArea } from 'antd-mobile';
import { money_US } from '@ali/iec-dtao-utils/esm/common/number';
import { LeftOutline, QuestionCircleOutline } from 'antd-mobile-icons';

import styles from './styles.module.css';

interface CouponPopupProps {
  visible: boolean;
  agreementPopVisible: boolean;
  effectiveDays: string;
  canNotQuitDays: string;
  couponList?: any;
  onCancel: () => void;
  onReceive: () => void;
  onAgreement: () => void;
  onAgreementBack: () => void;
  onCouponClose: () => void;
  onAgreementClose: () => void;
}

const CouponPopup = (props: CouponPopupProps) => {
  const {
    visible = false,
    agreementPopVisible = false,
    couponList = [],
    effectiveDays = '',
    canNotQuitDays = '',
    onCancel,
    onReceive,
    onAgreement,
    onAgreementBack,
    onCouponClose,
    onAgreementClose,
  } = props;

  return (
    <>
      <Popup
        visible={visible}
        showCloseButton
        closeOnMaskClick
        bodyStyle={{ maxHeight: '95vh' }}
        onClose={onCouponClose}
      >
        <div className={styles['coupon-popup-container']}>
          <div className={styles['coupon-popup-title']}>权益领取</div>
          <div className={styles['coupon-popup-content']}>
            <div className={styles['coupon-popup-header']}>
              <img src={COUPON_HEADER_IMG} />
            </div>
            <div className={styles['coupon-item-desc']}><img src={COUPON_CHECK_ICON} />领取后{effectiveDays || '--'}天内可享</div>
            <div className={styles['coupon-item-desc']}><img src={COUPON_CHECK_ICON} />开通后{canNotQuitDays || '--'}天内不可退出退货宝</div>
          </div>
          {/* 券列表 */}
          <div className={styles['coupon-list']}>
            {
              map(couponList, (coupon, index) =>
                (
                  <div className={styles['coupon-item']} key={index} >
                    <div className={styles['coupon-item-name']}>{coupon?.attributes?.title || '--'}</div>
                    <div className={styles['coupon-item-amount']}>
                      <span className={styles.estimate}>预估</span>
                      <span className={styles['discount-amount']}>{money_US(coupon.feeAmount?.value)}元/单</span>
                      <span className={styles['origin-amount']}>{money_US(coupon.originalFeeAmount?.value)}元/单</span>
                      <Popover
                        content="该价格为优惠前的价格"
                        trigger="click"
                        placement="bottom"
                      >
                        <QuestionCircleOutline style={{ width: '24rpx', height: '24rpx', color: '#fff' }} />
                      </Popover>
                    </div>
                    {couponList?.length > 1 && <div className={styles['coupon-banner-icon']}>权益{index + 1}</div>}
                    {index === 0 && <img src={COUPON_CAR_ICON} className={styles['coupon-car-icon']} />}
                  </div>
                ))
            }
          </div>
          {/* 协议 */}
          <div className={styles['coupon-popup-agreement']}>
            阅读<span onClick={() => onAgreement()}>《消费者体验提升计划协议》</span>
          </div>
          {/* 按钮 */}
          <div className={styles['coupon-popup-footer']}>
            <Button onClick={() => onCancel()}>狠心放弃
            </Button>
            <Button
              color="primary"
              onClick={() => onReceive()}
            >
              同意协议并领取权益
            </Button>
          </div>
          {/* 免责声明 */}
          <div className={styles['coupon-popup-footer-desc']}>点击“同意协议并领取优惠”后，您将开通“消费者体验提升计划”服务</div>
        </div>
        <SafeArea position="bottom" />
      </Popup>
      {/* 协议 */}
      <Popup
        visible={agreementPopVisible}
        closeOnMaskClick
        bodyStyle={{ maxHeight: '95vh', minHeight: '70vh' }}
        onClose={onAgreementClose}
      >
        <div className={styles.agreement}>
          <div className={styles.title}>协议详情</div>
          <iframe src={PROTOCOL_URL} />
          <LeftOutline className={styles.backIcon} onClick={() => onAgreementBack()} />
          <SafeArea position="bottom" />
        </div>
      </Popup>
    </>

  );
};

export default CouponPopup;
