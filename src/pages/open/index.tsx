import BottomBar from '@/components/BottomBar';
import Card from '@/components/Card';
import Explanation from '@/components/Explanation';
import Question from '@/components/Question';
import styles from './index.module.css';
import Header from '@/components/Header';
import NotAllow from '@/components/NotAllow';
import { useCallback, useEffect, useRef, useState } from 'react';
import { FixedAdmitAndTrailItem, BaseAdmitResponse, BaseOpenProps, batchAdmitFixedResponse, RenderInfo } from '@/types';
import { queryAlipayInfo, queryMerchantStatus } from '@/api/query';
import { baseAdmitAndTrailInfo, baseAndFixedOpen, baseOpen, batchAdmitFixedPrice, newMerchantsOpen } from '@/api/open';
import { getCookie } from '@/lib/cookie';
import DetailCard from '@/components/DetailCard';
import { useLoading } from '@/components/Loading';
import { definePageConfig } from 'ice';
import { log } from '@alife/dtao-iec-spm-log';
import { ByfOpenBtnMarketingTextApp, MOBILE_OPEN_CHANNEL, OPEN_FAILED_CODE, QIANNIU_H5, ByfAppOpenPoplayer } from '@/lib/constant';
import NoticeBar from '@/components/NoticeBar';
import { errorToast, loadingToast, successToast } from '@/lib/toast';
import BaseCouponPopup from './components/CouponPopup/index';
import { replace } from '@/lib/route';
import BasePopup from '@/components/Popup/base';
import { getUmidToken } from '@/lib/umid';
import { getUrlParams } from '@/lib/params';
import { queryDelivery } from '@/api/promotion';
import * as _ from 'lodash-es';
import PriceArea from '@/components/PriceArea';
import { handleAemLoad } from '@/lib/ameLog';
import useFatigue from '@/hooks/useFatigue';
import { Toast } from 'antd-mobile';

let authTimeId;

const Open = () => {
  const [alipayLoginId, setAlipayLoginId] = useState<string>('-');
  const [traceLog, setTraceLog] = useState<string | null>();
  const isClickApplyBtn = useRef(false);
  const isCouponPopClickApplyBtn = useRef(false);
  const { showLoading, hideLoading } = useLoading();
  // 用于记录当前选中的价格项索引
  const [selectedIndex, setSelectedIndex] = useState(0);
  // 渲染信息
  const [renderInfo, setRenderInfo] = useState<RenderInfo>([]);
  const [errorPopDesc, setErrorPopDesc] = useState<string | null>(null);
  const [openStatus, setOpenStatus] = useState<string>('');
  const {
    traceInfo,
    basePopInfo,
    couponPopInfo,
    setBasePopInfoFun,
    setCouponPopInfoFun,
  } = useFatigue({
    from: 'open',
    isCouponPopClickApplyBtn,
    status: openStatus,
    merchantStatusList: ['null', 'MERCHANT_QUIT_SUCCEED', 'MERCHANT_OPEN_FAILED'],
    positionCode: ByfAppOpenPoplayer,
  });

  // 切换开通类型
  const handleChangeSelected = useCallback((index: number) => {
    setSelectedIndex(index);
  }, [renderInfo]);

  const handleOnGetMarketInfo = async () => {
    try {
      const marketRes = await queryDelivery({ positionCode: ByfOpenBtnMarketingTextApp });
      const title = _.get(marketRes, 'content.items.0.fixedContent') ?? null;
      return title;
    } catch (error) {
      return null;
    }
  };

  const handleOnGetStatus = useCallback(
    async (checkStatus) => {
      try {
        const statusData = await queryMerchantStatus();
        const {
          status,
          responseCode: statusResponseCode,
          failedCode: statusFailedCode,
        } = statusData;
        if (statusResponseCode !== 'SUCCESS') {
          log.addLog('query-status', 'error', { responseCode: statusResponseCode });
          errorToast();
          return;
        }
        setOpenStatus(status);
        switch (status) {
          case 'null':
          case 'MERCHANT_QUIT_SUCCEED':
            hideLoading();
            break;
          case 'MERCHANT_OPENING':
            showLoading('正在开通中，请耐心等待');
            checkStatus();
            return;
          case 'MERCHANT_OPEN_FAILED':
            hideLoading();
            log.addLog('open-fail-access', 'error', {
              from: traceLog ?? null,
              code: statusFailedCode ?? 'DEFAULT_FRONT_ERROR',
            });
            /*
              * isClickApplyBtn: 开通页=>仅失败命中点击过开通，则提示失败文案
              * isCouponPopClickApplyBtn: 营销弹窗=>仅失败命中点击过营销弹窗开通，则提示失败文案
            */
            if (isClickApplyBtn.current === true || isCouponPopClickApplyBtn?.current === true) {
              setErrorPopDesc(
                statusFailedCode
                  ? OPEN_FAILED_CODE[statusFailedCode] || OPEN_FAILED_CODE?.DEFAULT_FRONT_ERROR
                  : OPEN_FAILED_CODE?.DEFAULT_FRONT_ERROR,
              );
              setErrorPopVisible(true);
              log.addLog(`open-fail-${statusFailedCode}`, 'success', { from: traceLog ?? null });
            }
            break;
          case 'MERCHANT_OPEN_SUCCEED':
          case 'MERCHANT_FROZEN':
          case 'MERCHANT_QUITTING':
            successToast('开通成功');
            log.addLog('open-success', 'success', { from: traceLog ?? null, priceType: getCurrentPriceInfo()?.fixedPriceType });
            replace('/detail');
            return;
          default:
        }
        // 并行发送日常准入试算和批量固定价准入试算
        showLoading('正在查询准入信息，请稍后');
        const [admitAndTrailResult, fixedAdmitAndTrailResult] = await Promise.allSettled([
          baseAdmitAndTrailInfo(),
          batchAdmitFixedPrice(),
        ]);
        if (admitAndTrailResult.status !== 'fulfilled') {
          log.addLog('base-admit-and-trail', 'error', { catch: admitAndTrailResult.reason?.message });
        }
        if (fixedAdmitAndTrailResult.status !== 'fulfilled') {
          log.addLog('fixed-admit-and-trail', 'error', { catch: fixedAdmitAndTrailResult.reason?.message });
        }

        const admitAndTrailData: BaseAdmitResponse = admitAndTrailResult.status === 'fulfilled' ? admitAndTrailResult.value : {
          isAdmit: false,
          hasHighRiskRefund: false,
          originalFeeAmount: '',
          fixedPriceType: 'YEAR',
        };
        const fixedAdmitAndTrailData: batchAdmitFixedResponse = fixedAdmitAndTrailResult.status === 'fulfilled' ? fixedAdmitAndTrailResult.value : {
          fixedAdmitAndTrailInfoList: [],
        };
        hideLoading();

        // 处理日常准入试算结果
        const {
          responseCode: admitAndTrailResponseCode,
          isAdmit: baseAdmit,
          originalFeeAmount: baseOriginalFeeAmount,
          hasHighRiskRefund: baseHasHighRiskRefund,
          promotionFlag: basePromotionFlag,
          discountFeeAmount: baseDiscountFeeAmount,
          customerRejectCode: baseCustomerRejectCode,
        } = admitAndTrailData;
        if (admitAndTrailResponseCode !== 'SUCCESS') {
          log.addLog('base-admit-and-trail', 'error', { responseCode: admitAndTrailResponseCode });
          return;
        }
        if (baseAdmit) {
          log.addLog('open-admit-access', 'other');
        } else {
          log.addLog('open-admit-reject', 'other', { rejectCode: baseCustomerRejectCode });
        }

        // 处理批量固定价准入试算结果
        const {
          responseCode: fixedAdmitAndTrailResponseCode,
          fixedAdmitAndTrailInfoList,
        } = fixedAdmitAndTrailData;
        if (fixedAdmitAndTrailResponseCode !== 'SUCCESS') {
          log.addLog('fixed-admit-and-trail', 'error', { responseCode: fixedAdmitAndTrailResponseCode });
        }

        const marketTitle = await handleOnGetMarketInfo();

        // 构建新的renderInfo数组
        const newRenderInfo: RenderInfo = [];

        // 先添加固定价信息 因为固定价显示在左边
        let firstFixedAccessIndex = -1;
        let hasFixedAccess = false;

        if (fixedAdmitAndTrailInfoList?.length > 0) {
          fixedAdmitAndTrailInfoList.forEach((item: FixedAdmitAndTrailItem) => {
            const fixedPriceInfo = {
              type: 'fixed' as const,
              isAccess: item.isAdmit ?? false,
              feeAmount: item.originalFeeAmount ?? '--',
              fixedPriceType: item.fixedPriceType ?? '',
              discountFeeAmount: item.discountFeeAmount ?? '--',
              promotionFlag: item.promotionFlag ?? false,
              marketTitle,
              effectTime: item.effectTime,
              expireTime: item.expireTime,
            };

            newRenderInfo.push(fixedPriceInfo);

            // 在添加的同时处理准入逻辑
            if (fixedPriceInfo.isAccess) {
              hasFixedAccess = true;
              // 记录第一个有准入的固定价索引（在newRenderInfo中的位置）
              if (firstFixedAccessIndex === -1) {
                firstFixedAccessIndex = newRenderInfo.length - 1;
              }
              // 打点
              log.addLog(`open-${item.fixedPriceType?.toLowerCase?.()}-admit-access`, 'other');
            }
          });
        }

        // 再添加日常价信息
        const basePriceIndex = newRenderInfo.length; // 日常价的索引位置
        newRenderInfo.push({
          type: 'base',
          isAccess: baseAdmit,
          feeAmount: baseOriginalFeeAmount,
          discountFeeAmount: baseDiscountFeeAmount,
          promotionFlag: basePromotionFlag,
          marketTitle,
          customerRejectCode: baseCustomerRejectCode,
          isHighRisk: baseHasHighRiskRefund,
        });

        setRenderInfo(newRenderInfo);

        // 设置默认选择（避免再次遍历数组）
        if (hasFixedAccess && firstFixedAccessIndex !== -1) {
          setSelectedIndex(firstFixedAccessIndex);
        } else {
          log.addLog('open-fixed-admit-reject', 'other');
          // 默认选中日常价（日常价现在在basePriceIndex位置）
          setSelectedIndex(basePriceIndex);
        }
      } catch (error) {
        log.addLog('base-admit-and-trail', 'error', { catch: error?.message });
      }
    },
    [hideLoading, showLoading, traceLog],
  );

  const checkUserStatus = () => {
    const checkStatus = () => {
      authTimeId = setTimeout(() => handleOnGetStatus(checkStatus), 2000);
    };
    handleOnGetStatus(checkStatus);
  };

  useEffect(() => {
    checkUserStatus();
    return () => clearTimeout(authTimeId);
  }, []);

  useEffect(() => {
    initLog();
    handleOnGetAlipayInfo();
  }, []);

  // 获取当前选中的价格信息
  const getCurrentPriceInfo = useCallback(() => {
    return renderInfo[selectedIndex] || renderInfo[0];
  }, [selectedIndex, renderInfo]);

  const initLog = () => {
    try {
      handleAemLoad();
      log.reportInit('a360n.BYF_MERCHANT_UNIAPP-open');
      const traceLogData = getUrlParams(window.location.href, 'traceLog');
      traceLogData && setTraceLog(traceLogData);
      log.addLog('byf-open', 'visit', { from: traceLogData ?? null });
    } catch (error) {
      log.addLog('byf-open', 'error', { catch: error?.message });
    }
  };

  const handleOnGetAlipayInfo = async () => {
    try {
      const alipayInfoData = await queryAlipayInfo({ channel: 'TRADE_ACCOUNT' });
      const { responseCode: alipayInfoResponseCode, alipayLoginId: alipayLoginIdData } =
        alipayInfoData;
      if (alipayInfoResponseCode !== 'SUCCESS') {
        log.addLog('query-alipay-info', 'error', { responseCode: alipayInfoResponseCode });
        return;
      }
      setAlipayLoginId(alipayLoginIdData);
    } catch (error) {
      log.addLog('query-alipay-info', 'error', { catch: error?.message });
    }
  };

  const handleOnApply = useCallback(
    async () => {
      showLoading('正在开通中，请耐心等待');
      isClickApplyBtn.current = true;
      const currentPriceInfo = getCurrentPriceInfo();

      // 日常开通
      if (currentPriceInfo.type === 'base') {
        log.addLog('open-base-click', 'click', { from: traceLog ?? null });
        try {
          const { aluUmid } = await getUmidToken();
          const params: BaseOpenProps = {
            identifier: `${getCookie()}-${Math.floor(Date.now())}`,
            showFeeAmount: currentPriceInfo.feeAmount,
            showDiscountFeeAmount: currentPriceInfo.discountFeeAmount,
            openChannel: MOBILE_OPEN_CHANNEL,
          };
          aluUmid && (params.umidToken = aluUmid);
          const applyData = await baseOpen(params);
          const { responseCode, applicationNo } = applyData;
          hideLoading();

          if (responseCode !== 'SUCCESS' || !applicationNo) {
            setErrorPopDesc(null);
            log.addLog('open-base-fail', 'error', { responseCode });
            setErrorPopVisible(true);
            return;
          }
          log.addLog('open-base-success', 'success', { from: traceLog ?? null });
          checkUserStatus();
        } catch (error) {
          hideLoading();
          setErrorPopDesc(null);
          setErrorPopVisible(true);
        }
      } else if (currentPriceInfo.type === 'fixed') {
        // 固定价+日常一起开通
        try {
          log.addLog('open-fixed-click', 'click', { priceType: currentPriceInfo?.fixedPriceType, from: traceLog ?? null });
          const { aluUmid } = await getUmidToken();
          const params: BaseOpenProps = {
            identifier: `${getCookie()}-${Math.floor(Date.now())}`,
            fixedPriceType: currentPriceInfo?.fixedPriceType,
            showFeeAmount: currentPriceInfo.feeAmount,
            showDiscountFeeAmount: currentPriceInfo.discountFeeAmount,
            fixedPriceEffectTime: currentPriceInfo?.effectTime ? currentPriceInfo.effectTime : undefined,
            fixedPriceExpireTime: currentPriceInfo?.expireTime ? currentPriceInfo.expireTime : undefined,
            openChannel: MOBILE_OPEN_CHANNEL,
          };
          aluUmid && (params.umidToken = aluUmid);
          const applyData = await baseAndFixedOpen(params);
          const { responseCode, applicationNo } = applyData;
          hideLoading();

          if (responseCode !== 'SUCCESS' || !applicationNo) {
            log.addLog('open-fixed-fail', 'error', { responseCode, from: traceLog ?? null, priceType: currentPriceInfo?.fixedPriceType });
            setErrorPopDesc(null);
            setErrorPopVisible(true);
            return;
          }
          log.addLog('open-fixed-success', 'success', { priceType: currentPriceInfo?.fixedPriceType, from: traceLog ?? null });
          checkUserStatus();
        } catch (error) {
          hideLoading();
          setErrorPopDesc(null);
          setErrorPopVisible(true);
        }
      }
    },
    [
      checkUserStatus,
      hideLoading,
      showLoading,
      selectedIndex,
      renderInfo,
      getCurrentPriceInfo,
    ],
  );

  // 开通
  const handleCouponReceiveBtn = async () => {
    try {
      isCouponPopClickApplyBtn.current = true;
      loadingToast('开通申请中...');
      log.addLog('coupon-popup-open', 'click', { from: traceLog ?? null });
      const { success, responseCode } = await newMerchantsOpen({
        identifier: `${getCookie()}-${Math.floor(Date.now())}`,
        openChannel: QIANNIU_H5,
        openScene: couponPopInfo?.openScene,
      });
      if (!success || responseCode !== 'SUCCESS') {
        Toast.show('系统异常，请稍后重试');
        log.addLog('coupon-popup-open-fail', 'error', { from: traceLog ?? null, responseCode });
        return;
      }
      log.addLog('coupon-popup-open', 'success', { from: traceLog ?? null });
      Toast.show('开通申请成功');
      checkUserStatus();
    } catch (error) {
      setCouponPopInfoFun({ couponPopVisible: false });
      log.addLog('coupon-popup-open-fail', 'error', { from: traceLog ?? null, error: JSON.stringify(error) });
      Toast.show('开通申请失败，请稍后重试');
    }
  };

  const [errorPopVisible, setErrorPopVisible] = useState(false);

  // 获取base类型的价格信息，用于判断是否准入（日常价总是在索引最后一位）
  const basePriceInfo = renderInfo[renderInfo.length - 1];

  return (
    <div className="base">
      {basePriceInfo?.isHighRisk && (
        <NoticeBar
          content="您店铺风险较高，本无法开通。为支持店铺经营，开放特殊开通通道，请确认费率后开通"
          colorType="alert"
          iconType="alert"
        />
      )}
      <Header />
      <div className={styles.content}>
        <div className={styles.panel}>
          {basePriceInfo?.isAccess ? (
            <>
              {/* <Card title="保障详情">
                <DetailCard alipayLoginId={alipayLoginId} />
              </Card>
              <Card title="">
                <PriceArea
                  renderInfo={renderInfo}
                  changeSelected={handleChangeSelected}
                />
              </Card>
              <Card title="保障说明">
                <Explanation />
              </Card> */}
            </>
          ) : (
            <NotAllow code={basePriceInfo?.customerRejectCode} alipayLoginId={alipayLoginId} />
          )}

          {/* <Card
            title="常见问题"
            className={`${basePriceInfo?.isAccess ? styles.endCard : styles.notAdmitEndCard}`}
          >
            <Question />
          </Card> */}
        </div>
        
        <div className={styles.bottomLinks}>
          <span className={styles.linkItem} onClick={()=>{replace('/coupon')}}>我的权益</span>
          <span className={styles.linkItem}>服务流程</span>
          <span className={styles.linkItem} onClick={()=>{replace('qa')}}>常见问题</span>
        </div>
      </div>
      {basePriceInfo?.isAccess && (() => {
        const currentPriceInfo = getCurrentPriceInfo();
        return (
          <BottomBar
            marketTitle={currentPriceInfo?.marketTitle}
            promotionFlag={currentPriceInfo?.promotionFlag}
            originalFeeAmount={currentPriceInfo?.feeAmount}
            discountFeeAmount={currentPriceInfo?.discountFeeAmount}
            onOpenClick={handleOnApply}
          />
        );
      })()}
      <BasePopup
        visible={errorPopVisible}
        setVisible={setErrorPopVisible}
        title="开通失败"
        type="error"
        desc={errorPopDesc || '开通失败，请稍后再试'}
      />
      <BaseCouponPopup
        ugTrack={traceInfo}
        basePopInfo={basePopInfo}
        couponPopInfo={couponPopInfo}
        onReceive={handleCouponReceiveBtn}
        setBasePopInfoFun={setBasePopInfoFun}
        setCouponPopInfoFun={setCouponPopInfoFun}
      />
    </div>
  );
};

export default Open;

export const pageConfig = definePageConfig(() => ({
  title: '退货宝',
}));
