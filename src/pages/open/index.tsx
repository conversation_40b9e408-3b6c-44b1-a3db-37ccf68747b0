import BottomBar from '@/components/BottomBar';
import Card from '@/components/Card';
import Explanation from '@/components/Explanation';
import Question from '@/components/Question';
import styles from './index.module.css';
import Header from '@/components/Header';
import NotAllow from '@/components/NotAllow';
import { useCallback, useEffect, useRef, useState } from 'react';
import { BaseOpenProps, OpenStatus } from '@/types';
import { queryAlipayInfo, queryMerchantStatus } from '@/api/query';
import { baseAdmitAndTrailInfo, baseOpen } from '@/api/open';
import { getCookie } from '@/lib/cookie';
import DetailCard from '@/components/DetailCard';
import { useLoading } from '@/components/Loading';
import { definePageConfig } from 'ice';
import { number, log } from '@ali/iec-dtao-utils';

import { ByfOpenBtnMarketingTextApp, MOBILE_OPEN_CHANNEL, OPEN_FAILED_CODE } from '@/lib/constant';
import NoticeBar from '@/components/NoticeBar';
import { errorToast, successToast } from '@/lib/toast';
import BasePopup from '@/components/Popup/base';
import { getUmidToken } from '@/lib/umid';
import { getUrlParams } from '@/lib/params';
import { replace } from '@/lib/route';
import { queryDelivery } from '@/api/promotion';
import * as _ from 'lodash-es';

const { money_US } = number;

export const pageConfig = definePageConfig(() => ({
  title: '退货宝',
  spm: {
    spmB: 'BYF_MERCHANT_UNIAPP-open',
  },
}));

let authTimeId;

const Open = () => {
  const [alipayLoginId, setAlipayLoginId] = useState<string>('-');
  const [traceLog, setTraceLog] = useState<string | null>();
  const isClickApplyBtn = useRef(false);

  const { showLoading, hideLoading } = useLoading();

  const [openInfo, setOpenInfo] = useState<{
    status: OpenStatus;
    isAccess: boolean;
    feeAmount: string;
    fixedPriceType: string;
    isHighRisk: boolean;
    discountFeeAmount?: string;
    promotionFlag?: boolean;
    promotionHalfOff?: boolean;
    promotionTitle?: string;
    marketTitle?: string | null;
    customerRejectCode?: string | null;
  }>({
    status: 'null',
    isAccess: true,
    feeAmount: '',
    fixedPriceType: 'YEAR',
    isHighRisk: false,
    discountFeeAmount: '',
    promotionFlag: false,
    promotionHalfOff: false,
    promotionTitle: '',
    marketTitle: null,
    customerRejectCode: null,
  });

  const [errorPopDesc, setErrorPopDesc] = useState<string | null>(null);

  const handleOnGetMarketInfo = async () => {
    try {
      const marketRes = await queryDelivery({ positionCode: ByfOpenBtnMarketingTextApp });
      const title = _.get(marketRes, 'content.items.0.fixedContent') ?? null;
      return title;
    } catch (error) {
      return null;
    }
  };

  const handleOnGetStatus = useCallback(
    async (checkStatus) => {
      try {
        const statusData = await queryMerchantStatus();
        const {
          status,
          responseCode: statusResponseCode,
          failedCode: statusFailedCode,
        } = statusData;
        if (statusResponseCode !== 'SUCCESS') {
          log.addLog('query-status', 'error', { responseCode: statusResponseCode });
          errorToast();

          return;
        }
        switch (status) {
          case 'null':
          case 'MERCHANT_QUIT_SUCCEED':
            hideLoading();
            break;
          case 'MERCHANT_OPENING':
            showLoading('正在开通中，请耐心等待');
            checkStatus();
            return;
          case 'MERCHANT_OPEN_FAILED':
            hideLoading();
            log.addLog('open-fail-access', 'error', {
              from: traceLog ?? null,
              code: statusFailedCode ?? 'DEFAULT_FRONT_ERROR',
            });
            // 仅失败命中点击过开通，则提示失败文案
            if (isClickApplyBtn.current === true) {
              setErrorPopDesc(
                statusFailedCode
                  ? OPEN_FAILED_CODE[statusFailedCode] || OPEN_FAILED_CODE?.DEFAULT_FRONT_ERROR
                  : OPEN_FAILED_CODE?.DEFAULT_FRONT_ERROR,
              );
              setErrorPopVisible(true);

              log.addLog(`open-fail-${statusFailedCode}`, 'success', { from: traceLog ?? null });
            }
            break;
          case 'MERCHANT_OPEN_SUCCEED':
          case 'MERCHANT_FROZEN':
          case 'MERCHANT_QUITTING':
            successToast('开通成功');

            log.addLog('open-success', 'success', { from: traceLog ?? null });
            replace('/detail');
            return;
          default:
        }

        const admitAndTrailData = await baseAdmitAndTrailInfo();
        const {
          responseCode: admitAndTrailResponseCode,
          isAdmit,
          originalFeeAmount,
          hasHighRiskRefund,
          fixedPriceType,
          promotionFlag,
          promotionHalfOff,
          promotionTitle,
          discountFeeAmount,
          customerRejectCode,
        } = admitAndTrailData;
        if (admitAndTrailResponseCode !== 'SUCCESS') {
          log.addLog('base-admit-and-trail', 'error', { responseCode: admitAndTrailResponseCode });
          return;
        }
        if (isAdmit) {
          log.addLog('open-admit-access', 'other');
        } else {
          log.addLog('open-admit-reject', 'other');
        }
        const marketTitle = await handleOnGetMarketInfo();
        setOpenInfo({
          status,
          isAccess: isAdmit,
          customerRejectCode,
          fixedPriceType,
          feeAmount: originalFeeAmount ?? '',
          isHighRisk: hasHighRiskRefund,
          discountFeeAmount: discountFeeAmount ?? '',
          promotionFlag,
          promotionHalfOff: promotionHalfOff ?? false,
          promotionTitle,
          marketTitle,
        });
      } catch (error) {
        log.addLog('base-admit-and-trail', 'error', { catch: error?.message });
      }
    },
    [hideLoading, showLoading, traceLog],
  );

  const checkUserStatus = () => {
    const checkStatus = () => {
      authTimeId = setTimeout(() => handleOnGetStatus(checkStatus), 2000);
    };
    handleOnGetStatus(checkStatus);
  };

  useEffect(() => {
    checkUserStatus();
    return () => clearTimeout(authTimeId);
  }, []);

  useEffect(() => {
    initLog();
    handleOnGetAlipayInfo();
  }, []);

  const initLog = () => {
    try {
      log.reportInit();
      const traceLogData = getUrlParams(window.location.href, 'traceLog');
      traceLogData && setTraceLog(traceLogData);
      log.addLog('byf-open', 'visit', { from: traceLogData ?? null });
    } catch (error) {
      log.addLog('byf-open', 'error', { catch: error?.message });
    }
  };

  const handleOnGetAlipayInfo = async () => {
    try {
      const alipayInfoData = await queryAlipayInfo({ channel: 'TRADE_ACCOUNT' });
      const { responseCode: alipayInfoResponseCode, alipayLoginId: alipayLoginIdData } =
        alipayInfoData;
      if (alipayInfoResponseCode !== 'SUCCESS') {
        log.addLog('query-alipay-info', 'error', { responseCode: alipayInfoResponseCode });
        return;
      }
      setAlipayLoginId(alipayLoginIdData);
    } catch (error) {
      log.addLog('query-alipay-info', 'error', { catch: error?.message });
    }
  };

  const handleOnApply = useCallback(
    async (checkInfo?: { isChecked?: boolean }) => {
      showLoading('正在开通中，请耐心等待');
      isClickApplyBtn.current = true;

      try {
        const { aluUmid } = await getUmidToken();
        const params: BaseOpenProps = {
          identifier: `${getCookie()}-${Math.floor(Date.now())}`,
          fixedPriceType: openInfo.fixedPriceType,
          showFeeAmount: openInfo.feeAmount,
          showDiscountFeeAmount: openInfo.discountFeeAmount,
          isChecked: checkInfo?.isChecked,
          openChannel: MOBILE_OPEN_CHANNEL,
          promotionHalfOff: openInfo?.promotionHalfOff,
        };

        aluUmid && (params.umidToken = aluUmid);

        const applyData = await baseOpen(params);
        const { responseCode, applicationNo, feeAmount } = applyData;
        hideLoading();
        // 价格不一致
        if (
          responseCode === 'FEE_AMOUNT_NOT_MATCH' ||
          responseCode === 'DISCOUNT_FEE_AMOUNT_NOT_MATCH'
        ) {
          log.addLog('open-amount-not-match', 'success', { responseCode });

          if (feeAmount) {
            // 如果价格不一致，则更新价格
            setOpenInfo({
              ...openInfo,
              feeAmount,
            });
          }

          setPriceNotMatchPopContent(
            `当前最新实时价格是${money_US(feeAmount)}元, 请确认是否开通？`,
          );
          setPriceNotMatchPopVisible(true);
          return;
        }
        // 其他失败
        if (responseCode !== 'SUCCESS' || !applicationNo) {
          log.addLog('open-fail', 'error', { responseCode });

          setErrorPopDesc(null);
          setErrorPopVisible(true);
          return;
        }
        checkUserStatus();
      } catch (error) {
        hideLoading();
        log.addLog('open-fail', 'error', { catch: error?.message });

        setErrorPopDesc(null);
        setErrorPopVisible(true);
      }
    },
    [
      checkUserStatus,
      hideLoading,
      openInfo.discountFeeAmount,
      openInfo.feeAmount,
      openInfo.fixedPriceType,
      openInfo?.promotionFlag,
      openInfo?.promotionHalfOff,
      openInfo?.promotionTitle,
      openInfo?.marketTitle,
      showLoading,
    ],
  );

  const [errorPopVisible, setErrorPopVisible] = useState(false);
  const [priceNotMatchPopVisible, setPriceNotMatchPopVisible] = useState(false);
  const [priceNotMatchPopContent, setPriceNotMatchPopContent] = useState('');

  const handlePriceNotMatchClose = useCallback(() => {
    log.addLog('open-click-cancel', 'click');
    window.location.reload();
  }, []);

  const handlePriceNotMatchOk = useCallback(() => {
    log.addLog('open-click-double-confirm', 'click', { from: traceLog ?? null });
    handleOnApply({ isChecked: true });
  }, [handleOnApply, traceLog]);

  const handlePriceNotMatchCancel = useCallback(() => {
    log.addLog('open-click-cancel', 'click');
    window.location.reload();
  }, []);

  return (
    <div className="base">
      {openInfo.isHighRisk && (
        <NoticeBar
          content="您店铺风险较高，本无法开通。为支持店铺经营，开放特殊开通通道，请确认费率后开通"
          colorType="alert"
          iconType="alert"
        />
      )}
      <Header />
      {openInfo.isAccess ? (
        <>
          <Card title="保障详情">
            <DetailCard alipayLoginId={alipayLoginId} />
          </Card>
          <Card title="保障说明">
            <Explanation />
          </Card>
        </>
      ) : (
        <NotAllow code={openInfo?.customerRejectCode} alipayLoginId={alipayLoginId} />
      )}

      <Card
        title="常见问题"
        className={`${openInfo.isAccess ? styles.endCard : styles.notAdmitEndCard}`}
      >
        <Question />
      </Card>

      {openInfo.isAccess && (
        <BottomBar
          promotionTitle={openInfo.promotionTitle}
          marketTitle={openInfo.marketTitle}
          promotionFlag={openInfo.promotionFlag}
          promotionHalfOff={openInfo.promotionHalfOff}
          originalFeeAmount={openInfo.feeAmount}
          discountFeeAmount={openInfo.discountFeeAmount}
          onOpenClick={handleOnApply}
        />
      )}
      <BasePopup
        visible={errorPopVisible}
        setVisible={setErrorPopVisible}
        title="开通失败"
        type="error"
        desc={errorPopDesc || '开通失败，请稍后再试'}
      />
      <BasePopup
        type="warn"
        visible={priceNotMatchPopVisible}
        setVisible={setPriceNotMatchPopVisible}
        title="价格不一致"
        desc={priceNotMatchPopContent}
        hasCancel
        onCancel={handlePriceNotMatchCancel}
        onOk={handlePriceNotMatchOk}
        onClose={handlePriceNotMatchClose}
        okText="确认开通"
        cancelText="我再想想"
      />
    </div>
  );
};

export default Open;
