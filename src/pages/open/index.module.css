.endCard {
  margin-bottom: calc(196rpx + var(--safe-area-inset-bottom, 0px));
}

.notAdmitEndCard {
  margin-bottom: var(--safe-area-inset-bottom);
}

.content{
  position: fixed;
  top: 450rpx;
  left: 0;
  right: 0;
  bottom: 0;
  background: transparent;
  z-index: 2;
  padding: 0;
  display: flex;
  flex-direction: column;
}

.panel{
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: stretch;
  min-height: 200rpx;
  border-radius: 24rpx 24rpx 0rpx 0rpx;
  background: #FFFFFF;
  margin: -28rpx 0rpx 0rpx 0rpx;
  padding: 40rpx 32rpx 40rpx 32rpx;
  box-shadow: 0 -4rpx 20rpx rgba(0, 0, 0, 0.08);
  flex-shrink: 0;
}

.bottomLinks {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 40rpx;
  padding: 40rpx 32rpx;
  margin-top: auto;
  padding-bottom: calc(40rpx + var(--safe-area-inset-bottom, 0px));
}

.linkItem {
  font-size: 26rpx;
  color: #999999;
  cursor: pointer;
  user-select: none;
}