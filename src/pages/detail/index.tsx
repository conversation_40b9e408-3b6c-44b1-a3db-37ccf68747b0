import { definePageConfig } from 'ice';
import styles from './index.module.css';
import {
  ByfAppHomePoplayer,
  ByfHomeBtnMarketingTextApp,
  ICON_MAP,
  MOBILE_OPEN_CHANNEL,
  PartSettingText,
  QIANNIU_H5,
  QUIT_TYPE,
  RECEIVE_RESULT_ERROR_INFO,
  RECEIVE_RESULT_SUCCESS_INFO,
  STATUS_INFO,
  TRADE_ACCOUNT,
} from '@/lib/constant';
import DetailCard, { DetailInfo } from '@/components/DetailCard';
import Card from '@/components/Card';
import Question from '@/components/Question';
import { Button, SafeArea, Toast, NoticeBar as AntdNoticeBar } from 'antd-mobile';
import { useCallback, useEffect, useRef, useState } from 'react';
import AgreementPopup from '@/components/Popup/agreements';
import InterestConfirm from '@/components/Popup/interestConfirm';
import RedEnvelopeConfirm from '@/components/Popup/redEnvelopeConfirm';
import QuitFormPopup from '@/components/Popup/quitForm';
import { log } from '@ali/iec-dtao-utils';
import { animationDelay } from '@/lib/tools';
import { baseQuit, queryBatchTrail, quitAdmit, receiveRedEnvelope } from '@/api/quit';
import { getCookie } from '@/lib/cookie';
import { baseTrail, changePartSetting, fixedAdmitAndTrailInfo, fixedRenewalAdmit, fixedRenewalTrail, fixedTrail, giveAdmit } from '@/api/open';
import { queryAlipayInfo, queryDataIndicatorList, queryMerchantArrears, queryMerchantStatus } from '@/api/query';
import { errorToast, loadingToast, successToast } from '@/lib/toast';
import BasePopup from '@/components/Popup/base';
import { replace } from '@/lib/route';
import NoticeBar from '@/components/NoticeBar';
import { queryDelivery } from '@/api/promotion';
import * as _ from 'lodash-es';
import useTips from '@/components/Tips';
import PartSetting from '@/components/PartSetting';
import { CloseCircleFill, RightOutline } from 'antd-mobile-icons';
import PromotionDialog from './components/PromotionDialog';
import { money_US } from '@ali/iec-dtao-utils/esm/common/number';
import numeral from 'numeral';
import dayjs from 'dayjs';
import FixedRenewalTrail from '@/components/Popup/fixedRenewalTrail';

const HEADER_ICON =
  'https://gw.alicdn.com/imgextra/i4/O1CN01kNbz5s27qtib0hPkL_!!6000000007849-2-tps-162-51.png';


const renderIndictor = (value, unit) => {
  if (unit === '元' && value) {
    return money_US(value);
  }
  if (value) {
    return numeral(value).format('0,0');
  }
  return <div className={styles.dataNumTag}>待更新</div>;
};

const Detail = () => {
  const [agreementVisible, setAgreementVisible] = useState(false);
  const [quitConfirmVisible, setQuitConfirmVisible] = useState(false);
  const [quitFormVisible, setQuitFormVisible] = useState(false);

  const [renderContent, setRenderContent] = useState<{
    status: string;
    isOpenFixed?: boolean; // 是否开固定价
    isFixedAdmit?: boolean; // 固定价准入
    arrearsAmount?: string; // 欠费金额
    effectTime: number | null; // 固定价生效时间
    expireTime: number | null; // 固定价失效时间
    baseFeeAmount?: string; // 日常试算金额
    fixedFeeAmount?: string; // 固定价试算金额
    fixedPriceType?: string; // 固定价类型
    discountFeeAmount?: string; // 固定价类型
    promotionFlag?: boolean; // 是否营销
    promotionHalfOff?: boolean; // 是否5折
    promotionTitle?: string; // 营销标题
    marketIcon?: string | null; // 用增icon
    marketTitle?: string | null; // 用增标题
    customerFreezeCode?: string | null; // 冻结码
    giveType: string | null; // 服务设置
    currFixedPriceExpireSoon?: boolean | null; // 当前一口价是否即将过期
    currFixedPriceRenewal?: boolean | null; // 前一口价是否已续签
    isFixedRenewalAdmit?: boolean; // 年框续签准入
    currFixedPriceExpireTime?: string | number | null; // 当前年框即将到期时间
  }>({
    status: 'MERCHANT_OPEN_SUCCEED',
    isFixedAdmit: false,
    fixedPriceType: '',
    baseFeeAmount: '',
    fixedFeeAmount: '',
    effectTime: null,
    expireTime: null,
    discountFeeAmount: '',
    promotionFlag: false,
    promotionHalfOff: false,
    promotionTitle: '',
    marketIcon: null,
    marketTitle: null,
    customerFreezeCode: null,
    giveType: null,
    currFixedPriceExpireSoon: null,
    currFixedPriceRenewal: null,
    isFixedRenewalAdmit: false,
    currFixedPriceExpireTime: null,
  });

  const [arrearsInfo, setArrearsInfo] = useState<{ isArrears: boolean; showArrears: boolean }>({ isArrears: false, showArrears: false });
  const [partAdmit, setPartAdmit] = useState<boolean>(false);
  const [showPartSetting, setShowPartSetting] = useState<boolean>(false);

  // 年框续签数据
  const [fixedRenewalData, setFixedRenewalData] = useState<{
    originalFeeAmount: string;
    effectTime?: number | null;
    expireTime?: number | null;
  }>({
    originalFeeAmount: '',
    effectTime: null,
    expireTime: null,
  });

  const detailInfo: DetailInfo = {
    status: renderContent.status,
    fixedFeeAmount: renderContent.fixedFeeAmount,
    baseFeeAmount: renderContent.baseFeeAmount,
    promotionFlag: renderContent.promotionFlag,
    promotionHalfOff: renderContent.promotionHalfOff,
    promotionTitle: renderContent.promotionTitle,
    marketIcon: renderContent.marketIcon,
    marketTitle: renderContent.marketTitle,
    discountFeeAmount: renderContent.discountFeeAmount,
    effectTime: renderContent.effectTime,
    expireTime: renderContent.expireTime,
    isOpenFixed: renderContent.isOpenFixed,
  };

  const [admitFailVisible, setAdmitFailVisible] = useState<boolean>(false);
  const [noAdmitDesc, setNoAdmitDesc] = useState<string>('');
  const [quitPopType, setQuitPopType] = useState<string>('');
  const [redEnvelopeInfo, setRedEnvelopeInfo] = useState<any>({});
  const [receiveInfo, setReceiveInfo] = useState<any>({});
  const [redEnvelopeVisible, setRedEnvelopeVisible] = useState<boolean>(false);
  const [receiveResultVisible, setReceiveResultVisible] = useState<boolean>(false);

  const [showPartServiceData, setShowPartServiceData] = useState<boolean>(false);
  const [showFixedRenewalTrail, setShowFixedRenewalTrail] = useState<boolean>(false);

  const [canNotExitPopVisible, setCanNotExitPopVisible] = useState(false);
  const statusDisplayInfo = STATUS_INFO[renderContent.status];
  const [dataUpdateDeadline, setDataUpdateDeadline] = useState<number | null>(null);
  const [indicatorData, setIndicatorData] = useState<Array<{
    title?: string;
    id?: string;
    value?: string;
    unit?: null | string;
  }>>([]);
  const [isShowAll, setIsShowAll] = useState<boolean>(false);

  const disableQuit =
    renderContent.status === 'MERCHANT_QUITTING' || renderContent.status === 'MERCHANT_FROZEN';
  // 区分前置准入和后置准入
  const admitType = useRef<any>(null);

  const handleAgreementClick = useCallback(() => {
    setAgreementVisible(true);
  }, []);

  const handleOnGetMarketInfo = async () => {
    try {
      const marketRes = await queryDelivery({ positionCode: ByfHomeBtnMarketingTextApp });
      return marketRes;
    } catch (error) {
      return null;
    }
  };

  const handleQuitClick = useCallback(() => {
    if (disableQuit) {
      return;
    }

    if (renderContent.isOpenFixed) {
      setCanNotExitPopVisible(true);
      return;
    }
    quitAdmitRequest();
  }, [disableQuit, renderContent.isOpenFixed]);

  // 退出准入
  const quitAdmitRequest = async () => {
    try {
      loadingToast('加载中...');
      const data = await quitAdmit({ channel: QIANNIU_H5 });
      const { success, isAdmit, attributes } = data;
      const { quitRetainPopupType, rejectRuleList } = attributes || {};
      admitType.current = 'pre';
      Toast.clear();
      if (!success) {
        log.addLog('quit-pre-admit-request-fail', 'error', { error: 'SYSTEM_ERROR' });
        errorToast('系统异常, 请稍后再试');
        return;
      }

      if (!isAdmit) {
        log.addLog('quit-pre-admit-reject-popup-init', 'visit');
        const { name, beginTime, endTime } = _.get(rejectRuleList, '[0]');
        const checkTime = (time, formatRule) => (time && formatRule ? dayjs(time).format(formatRule) : '--');
        setNoAdmitDesc(`您于${checkTime(beginTime, 'YYYY-MM-DD')}参与${name || '--'}活动享受专属优惠，${checkTime(endTime, 'YYYY-MM-DD HH:mm:ss')}前无法退出，感谢您的理解`);
        setAdmitFailVisible(true);
        return;
      }
      setQuitPopType(quitRetainPopupType);
      setQuitConfirmVisible(true);
    } catch (error) {
      Toast.clear();
      log.addLog('quit-pre-admit-request-fail', 'error', { error: 'SYSTEM_ERROR' });
    }
  };

  const handleSubmitQuitForm = useCallback((reason: string[]) => {
    setQuitFormVisible(false);
    handleOnApplyQuit(reason);
  }, []);

  // 消费者退出确认
  const handleConfirmQuit = useCallback(() => {
    setQuitConfirmVisible(false);
    queryBatchTrailRequest();
  }, []);
  const [alipayLoginId, setAlipayLoginId] = useState<string>('');

  // 试算信息
  const queryBatchTrailRequest = async () => {
    try {
      loadingToast('退出中...');
      const batchTrailRes = await queryBatchTrail({ channel: QIANNIU_H5 });
      const { success, canSendCoupon, serviceFeeDetails, attributes } = batchTrailRes;
      const { currentServiceFee, discountServiceFee } = serviceFeeDetails || {};
      const { discount, showDiscount, effectiveDays, canNotQuitDays } = attributes || {};

      Toast.clear();
      setQuitConfirmVisible(false);
      await animationDelay();

      if (!success) {
        log.addLog('query-trail-fail', 'error', { error: batchTrailRes });
        setQuitFormVisible(true);
        return;
      }

      // 没有试算信息
      if (!canSendCoupon) {
        log.addLog('query-trail-no-info', 'error', { error: batchTrailRes });
        setQuitFormVisible(true);
        return;
      }
      setRedEnvelopeInfo({ currentServiceFee, discountServiceFee, showDiscount, effectiveDays, canNotQuitDays, discount });
      setRedEnvelopeVisible(true);
    } catch (error) {
      Toast.clear();
      setQuitConfirmVisible(false);
      await animationDelay();
      setQuitFormVisible(true);
      log.addLog('query-trail-fail', 'error', { error: 'QUERY-BATCH-TRAIL-FAIL' });
    }
  };

  // 退出申请
  const handleOnApplyQuit = async (data) => {
    const reasonData = data ? data.join(',') : null;
    log.addLog('quit-reason-apply-click', 'success');
    try {
      // 退出准入
      const adminResult = await quitAdmit({ channel: QIANNIU_H5 });
      const { success, isAdmit, attributes } = adminResult;
      admitType.current = 'later';
      if (!success) {
        log.addLog('quit-later-admit-request-fail', 'error', { error: 'SYSTEM_ERROR' });
        errorToast('系统异常,请稍后再试');
        return;
      }

      if (!isAdmit) {
        log.addLog('quit-later-admit-reject-popup-init', 'visit');
        setQuitFormVisible(false);
        const { name, beginTime, endTime } = _.get(attributes?.rejectRuleList, '[0]');
        const checkTime = (time, formatRule) => (time && formatRule ? dayjs(time).format(formatRule) : '--');
        setNoAdmitDesc(`您于${checkTime(beginTime, 'YYYY-MM-DD')}参与${name || '--'}活动享受专属优惠，${checkTime(endTime, 'YYYY-MM-DD HH:mm:ss')}前无法退出，感谢您的理解`);
        setAdmitFailVisible(true);
        return;
      }

      log.addLog('quit-reason', 'success', { reason: reasonData });
      // 退出申请
      successToast('退出申请提交成功');
      const params = {
        identifier: `${getCookie()}-${Math.floor(Date.now())}`,
        scene: QUIT_TYPE,
        reason: reasonData,
      };
      const quitData = await baseQuit(params);
      const { responseCode, applicationNo } = quitData;
      if (responseCode !== 'SUCCESS' || !applicationNo) {
        log.addLog('quit-reason-apply', 'error', { responseCode });
        errorToast('退出申请中失败，请稍后重试');
        return;
      }
      log.addLog('quit-reason-apply', 'success');

      successToast('退出申请提交成功', {
        duration: 3000,
        maskClickable: false,
      });

      await animationDelay(2000);
      window.location.reload();
    } catch (error) {
      log.addLog('quit-reason-apply', 'error');
    }
  };

  const handleOnGetStatus = async () => {
    try {
      const statusData = await queryMerchantStatus();
      const {
        status,
        responseCode: statusResponseCode,
        openFixedPrice,
        fixedPriceType: statusFixedPriceType,
        customerFreezeCode: customerFreezeCodeData,
        giveType: giveTypeData,
        currFixedPriceExpireSoon: currFixedPriceExpireSoonData,
        currFixedPriceRenewal: currFixedPriceRenewalData,
        currFixedPriceExpireTime: currFixedPriceExpireTimeData,
      } = statusData;
      if (statusResponseCode !== 'SUCCESS') {
        log.addLog('query-status', 'error', { responseCode: statusResponseCode });
        errorToast();
        return;
      }
      switch (status) {
        case 'null':
        case 'MERCHANT_OPENING':
        case 'MERCHANT_OPEN_FAILED':
        case 'MERCHANT_QUIT_SUCCEED':
          replace('/open');
          return;
        case 'MERCHANT_FROZEN':
        case 'MERCHANT_QUITTING':
          setRenderContent({
            status,
            effectTime: null,
            expireTime: null,
            isOpenFixed: openFixedPrice,
            customerFreezeCode: customerFreezeCodeData,
            giveType: giveTypeData,
            currFixedPriceExpireSoon: currFixedPriceExpireSoonData,
            currFixedPriceRenewal: currFixedPriceRenewalData,
            currFixedPriceExpireTime: currFixedPriceExpireTimeData,
          });
          return;
        default:
      }
      // 未开固定价: 日常试算+固定价准入+固定价试算
      if (!openFixedPrice) {
        // 日常试算
        const baseTrailData = await baseTrail();
        const {
          responseCode: admitAndTrailResponseCode,
          originalFeeAmount: baseFeeAmountData,
          discountFeeAmount: discountFeeAmountData,
          promotionFlag,
          promotionHalfOff,
          promotionTitle,
        } = baseTrailData;
        if (admitAndTrailResponseCode !== 'SUCCESS') {
          log.addLog('query-base-trail', 'error', { responseCode: admitAndTrailResponseCode });
          errorToast();
        }
        // 固定价准入+固定价试算
        const fixedAdmitData = await fixedAdmitAndTrailInfo();
        const {
          isAdmit,
          expireTime,
          originalFeeAmount: fixedFeeAmountData,
          fixedPriceType,
          effectTime,
        } = fixedAdmitData;
        if (isAdmit) {
          log.addLog('manage-fixed-open-access', 'other');
        }

        // 用增营销位
        const marketRes = await handleOnGetMarketInfo();
        const marketIconData = _.get(marketRes, 'content.items.0.iconUrl') ?? null;
        const marketTitleData = _.get(marketRes, 'content.items.0.fixedContent') ?? null;

        setRenderContent({
          status,
          isOpenFixed: openFixedPrice,
          isFixedAdmit: isAdmit,
          effectTime: effectTime ?? null,
          expireTime: expireTime ?? null,
          fixedPriceType,
          discountFeeAmount: discountFeeAmountData,
          baseFeeAmount: baseFeeAmountData,
          fixedFeeAmount: fixedFeeAmountData,
          promotionFlag,
          promotionHalfOff,
          promotionTitle,
          marketIcon: marketIconData,
          marketTitle: marketTitleData,
          giveType: giveTypeData,
        });
        return;
      }
      // 已开固定价：固定价试算
      const fixedData = await fixedTrail({
        fixedPriceType: statusFixedPriceType,
        openChannel: MOBILE_OPEN_CHANNEL,
      });
      const {
        responseCode: fixedResponseCode,
        originalFeeAmount: fixedFeeAmountData,
        effectTime,
        expireTime,
        discountFeeAmount,
      } = fixedData;
      if (fixedResponseCode !== 'SUCCESS') {
        log.addLog('query-fixed-trail', 'error', { responseCode: fixedResponseCode });
        errorToast();
      }
      let isFixedRenewalAdmitData = false;
      // 年框即将到期
      if (currFixedPriceExpireSoonData) {
        // 年框续约准入
        const fixedRenewalAdmitRes = await fixedRenewalAdmit({ openChannel: QIANNIU_H5 });
        const {
          responseCode,
          isAdmit,
        } = fixedRenewalAdmitRes;
        if (responseCode !== 'SUCCESS') {
          log.addLog('fixed-renewal-admit', 'error', { responseCode });
        }
        isFixedRenewalAdmitData = isAdmit;
      }
      // 年框已续约
      if (currFixedPriceExpireSoonData && currFixedPriceRenewalData) {
        await handleOnFixedRenewalTrail();
      }
      setRenderContent({
        status,
        isOpenFixed: openFixedPrice,
        isFixedAdmit: true,
        effectTime: effectTime ?? null,
        expireTime: expireTime ?? null,
        fixedPriceType: statusFixedPriceType,
        fixedFeeAmount: fixedFeeAmountData,
        discountFeeAmount,
        giveType: giveTypeData,
        currFixedPriceExpireSoon: currFixedPriceExpireSoonData,
        currFixedPriceRenewal: currFixedPriceRenewalData,
        isFixedRenewalAdmit: isFixedRenewalAdmitData,
        currFixedPriceExpireTime: currFixedPriceExpireTimeData,
      });
    } catch (error) {
      log.addLog('query-info', 'error', { catch: error?.message });
      errorToast();
    }
  };

  // 年框续约试算（未开通||待生效）
  const handleOnFixedRenewalTrail = async () => {
    try {
      const fixedRenewalTrailRes = await fixedRenewalTrail({
        fixedPriceType: renderContent?.fixedPriceType,
        openChannel: QIANNIU_H5,
        currFixedPriceRenewal: renderContent?.currFixedPriceRenewal,
      });
      const { success, responseCode, originalFeeAmount: originalFeeAmountData, effectTime: effectTimeData, expireTime: expireTimeData } = fixedRenewalTrailRes;
      if (!success || responseCode !== 'SUCCESS' || !originalFeeAmountData) {
        log.addLog('fixed-renewal-trail', 'error', { responseCode });
        return;
      }
      setFixedRenewalData({
        originalFeeAmount: originalFeeAmountData,
        effectTime: effectTimeData,
        expireTime: expireTimeData,
      });
    } catch (error) {
      log.addLog('fixed-renewal-trail', 'error');
    }
  };

  const handleOnGetAlipayInfo = async () => {
    try {
      const alipayInfoData = await queryAlipayInfo({ channel: TRADE_ACCOUNT });
      const { responseCode: alipayInfoResponseCode, alipayLoginId: alipayLoginIdData } =
        alipayInfoData;
      if (alipayInfoResponseCode !== 'SUCCESS') {
        log.addLog('query-alipay-info', 'error', { responseCode: alipayInfoResponseCode });
        return;
      }
      setAlipayLoginId(alipayLoginIdData);
    } catch (error) {
      log.addLog('query-alipay-info', 'error', { catch: error?.message });
    }
  };

  const handleOnGetDataIndicatorList = async () => {
    try {
      const dataIndicatorList = await queryDataIndicatorList();
      const { responseCode, indicatorProcessData, endTime } = dataIndicatorList;
      if (responseCode !== 'SUCCESS') {
        log.addLog('query-data-indicator', 'error', { responseCode });
        return;
      }
      log.addLog('query-data-indicator', 'success');
      setIndicatorData(indicatorProcessData);
      setDataUpdateDeadline(endTime);
    } catch (error) {
      log.addLog('query-data-indicator', 'error');
    }
  };

  const handleOnGetArrearsAmount = async () => {
    try {
      const arrearsData = await queryMerchantArrears();
      const { responseCode, arrears, showArrears } = arrearsData;
      if (responseCode !== 'SUCCESS') {
        log.addLog('query-arrears-amount', 'error', { responseCode });
        return;
      }
      setArrearsInfo({ showArrears, isArrears: arrears });
    } catch (error) {
      log.addLog('query-arrears-amount', 'error', { catch: error?.message });
    }
  };

  // 赠送准入
  const handleOnGiveAdmit = async () => {
    try {
      const params = {
        openChannel: MOBILE_OPEN_CHANNEL,
      };
      const giveAdmitRes = await giveAdmit(params);
      const { responseCode, status: giveAdmitStatus } = giveAdmitRes;
      if (responseCode !== 'SUCCESS') {
        log.addLog('query-give-admit', 'error', { responseCode });
        return;
      }
      setPartAdmit(giveAdmitStatus === 'PASS');
    } catch (error) {
      log.addLog('query-give-admit', 'error');
    }
  };

  const tips = useTips(
    {
      isArrears: arrearsInfo.isArrears && arrearsInfo.showArrears,
      status: renderContent.status,
      isOpenFixed: renderContent?.isOpenFixed,
      customerFreezeCode: renderContent?.customerFreezeCode,
    },
  );

  const handleOnPartChange = async (value) => {
    loadingToast('正在切换服务设置，请稍后');
    try {
      const params = {
        channel: MOBILE_OPEN_CHANNEL,
        identifier: `${getCookie()}-${Math.floor(Date.now())}`,
        giveType: value,
      };
      const res = await changePartSetting(params);
      const { responseCode } = res;
      if (_.includes(['SUCCESS', 'USER_OPERATION_SUCCEED'], responseCode)) {
        successToast('设置成功');
        window.location.reload();
        return;
      }
      throw new Error();
    } catch (error) {
      errorToast('切换失败，请稍后再试');
    }
  };

  // 红包弹窗确认退出
  const handleRedEnvelopeBtn = async () => {
    setRedEnvelopeVisible(false);
    await animationDelay();
    setQuitFormVisible(true);
  };

  // 立即领取
  const handleReceiveEnvelopeBtn = async () => {
    try {
      loadingToast('领取中...');
      const { discount, showDiscount, effectiveDays } = redEnvelopeInfo || {};
      const res = await receiveRedEnvelope({
        identifier: `${getCookie()}-${Math.floor(Date.now())}`,
        expectDiscount: discount?.value,
        expectShowDiscount: showDiscount?.value,
        expectEffectiveDays: effectiveDays,
      });
      const { success, sendCoupon, responseCode, attributes } = res;

      Toast.clear();
      setRedEnvelopeVisible(false);

      if (!success) {
        log.addLog('receive-red-envelope-fail', 'error', { errorCode: 'SYSTEM_ERROR' });
        errorToast('系统异常, 请稍后再试');
        return;
      }

      if (_.includes(['DYNAMIC_PARAM_INVALID'], responseCode)) {
        log.addLog('receive-red-envelope-fail', 'error', { errorCode: 'DYNAMIC_PARAM_INVALID' });
        errorToast('数据更新，请稍候');
        return;
      }

      // 领取失败
      if (!sendCoupon) {
        const errorCode = _.get(attributes, 'errorCode');
        if (_.includes(['ERROR-8888'], errorCode)) {
          Toast.show({
            icon: <CloseCircleFill />,
            content: <div className={styles.errorToast}>抱歉，当前系统异常，稍后您可尝试重新领取</div>,
          });
          return;
        }
        setReceiveInfo(RECEIVE_RESULT_ERROR_INFO[errorCode] || RECEIVE_RESULT_ERROR_INFO.default);
        await animationDelay();
        setReceiveResultVisible(true);
        return;
      }

      // 领取成功
      setReceiveInfo(RECEIVE_RESULT_SUCCESS_INFO);
      await animationDelay();
      setReceiveResultVisible(true);
    } catch (error) {
      Toast.clear();
      log.addLog('receive-red-envelope-fail', 'error', { error: 'SYSTEM_ERROR' });
      setRedEnvelopeVisible(false);
    }
  };

  // 领取红包二次确认弹窗
  const handleReceiveResultBtn = async () => {
    log.addLog('receive-result-popup-ok-click', 'success', { code: receiveInfo?.code });
    switch (receiveInfo?.code) {
      case 'success': // 领取成功
        setReceiveResultVisible(false);
        window.location.reload();
        break;
      case 'ERROR-2001': // 超出单人发放疲惫度
      case 'ERROR-4017': // 超出总体发放疲惫度
      default: // 不可重试其他异常
        setReceiveResultVisible(false);
        await animationDelay();
        setQuitFormVisible(true);
        break;
    }
  };

  const renderFixedRenewal = () => {
    // 未续约
    if (renderContent?.currFixedPriceExpireSoon && !renderContent?.currFixedPriceRenewal && renderContent?.isFixedRenewalAdmit) {
      return (
        <div className={styles['fixed-renewal-container']}>
          <AntdNoticeBar
            content={
              <div className={styles.noticeBarContent}>
                年框一口价将于{renderContent?.currFixedPriceExpireTime ? dayjs(renderContent?.currFixedPriceExpireTime).format('YYYY-MM-DD') : '--'} 到期，到期后将恢复日常浮动定价，当前可提前续签，请您尽快前往「千牛PC-金融服务-退货宝」完成续签
              </div>
            }
            color={'alert'}
            icon={<img src={ICON_MAP.alert} className={styles.noticeBarIcon} />}
            wrap
          />
        </div>
      );
    }
    // 已续约
    if (renderContent?.currFixedPriceExpireSoon && renderContent?.currFixedPriceRenewal) {
      return (
        <div className={styles['fixed-renewal-container']}>
          <AntdNoticeBar
            content={
              <div className={styles.noticeBarFixedRenewalContent}>
                <div>续签成功</div>
                <div
                  style={{ height: '36rpx' }}
                  onClick={async () => {
                    await handleOnFixedRenewalTrail();
                    setShowFixedRenewalTrail(true);
                  }}
                >
                  查看详情 &gt;
                </div>
              </div>
            }
            color={'info'}
            icon={<img src={ICON_MAP.info} className={styles.noticeBarIcon} />}
            wrap
          />
        </div>
      );
    }
    return <></>;
  };

  useEffect(() => {
    log.addLog('byf-manage', 'visit');
    log.reportInit();
    handleOnGetStatus();
    handleOnGetAlipayInfo();
    handleOnGetArrearsAmount();
    handleOnGetDataIndicatorList();
    handleOnGiveAdmit();
  }, []);

  useEffect(() => {
    receiveResultVisible && log.addLog('receive-result-popup-init', 'visit');
  }, [receiveResultVisible]);

  return (
    <div className="base">
      <PromotionDialog
        positionCode={ByfAppHomePoplayer}
      />
      {tips && <NoticeBar content={tips.content} type={tips.type} />}
      <div className={styles.header}>
        <img src={HEADER_ICON} className={styles.headerIcon} />
        <div>消费者体验提升计划</div>
        <div
          className={styles.headerStatus}
          style={{ color: statusDisplayInfo.color, background: statusDisplayInfo.bgColor }}
        >
          {statusDisplayInfo.title}
        </div>
      </div>
      <Card title="保障详情">
        {renderFixedRenewal()}
        <DetailCard alipayLoginId={alipayLoginId} detailInfo={detailInfo} />
      </Card>
      {
        partAdmit && (
          <Card
            title="服务设置"
            extra={
              <Button
                size="mini"
                fill="none"
                onClick={() => {
                  log.addLog('part-setting-menu-click', 'success');
                  setShowPartSetting(true);
                }}
              >
                {PartSettingText[renderContent?.giveType || 'null']} <RightOutline />
              </Button>
            }
          />
        )
      }
      {
        indicatorData?.length > 0 && (
          <Card title="保障数据" extra={dataUpdateDeadline ? `截止时间 ${dayjs(dataUpdateDeadline).format('YYYY-MM-DD HH:mm:ss')}` : ''}>
            <div className={styles.dataContainer}>
              {
                (isShowAll ? indicatorData : indicatorData.slice(0, Math.min(4, indicatorData.length)))?.map((item) => (
                  <div className={styles.dataContent}>
                    <div className={styles.dataTitle}>
                      {item?.title}
                    </div>
                    <div className={styles.dataNum}>
                      {renderIndictor(item?.value, item?.unit)}
                      {
                        item?.value && item?.unit && <span>{item?.unit}</span>
                      }
                    </div>
                  </div>))}
            </div>
            {
              indicatorData?.length > 4 &&
              (
                <div
                  className={styles.showUpPanelButton}
                  onClick={() => { setIsShowAll(!isShowAll); }}
                >
                  {
                    isShowAll ?
                      <div className={styles.btnViewPanel}>
                        <div className={styles.btnViewText}>收起</div>
                        <div className={styles.btnViewDown} />
                      </div> :
                      <div className={styles.btnViewPanel}>
                        <div className={styles.btnViewText}>展开</div>
                        <div className={styles.btnViewUp} />
                      </div>
                  }
                </div>
              )
            }
          </Card>
        )
      }
      <Card title="常见问题">
        <Question />
      </Card>
      <div
        className={`${styles.exitContainer} ${disableQuit ? styles.disable : ''}`}
        onClick={handleQuitClick}
      >
        退出服务
      </div>
      <div className={styles.linkContainer}>
        <a onClick={handleAgreementClick}>《消费者体验提升计划协议》</a>
      </div>
      <SafeArea position="bottom" />
      <AgreementPopup visible={agreementVisible} setVisible={setAgreementVisible} />
      <BasePopup
        visible={canNotExitPopVisible}
        setVisible={setCanNotExitPopVisible}
        type="warn"
        title="暂无法退出"
        desc="对不起，你已经参加了承诺参与固定天数的活动，活动有效期内无法退出"
        okText="我知道了"
      />
      {/* 领取红包二次确认弹窗 */}
      <BasePopup
        visible={receiveResultVisible}
        hasCancel={receiveInfo?.hasCancel}
        setVisible={setReceiveResultVisible}
        onOk={handleReceiveResultBtn}
        type={receiveInfo?.type}
        title={receiveInfo?.title}
        desc={receiveInfo?.desc}
        okText={receiveInfo?.okText}
        onCancel={() => {
          log.addLog('receive-result-popup-cancel-click', 'success', { code: receiveInfo?.code });
        }}
      />
      {/* 退出不准入 */}
      <BasePopup
        visible={admitFailVisible}
        setVisible={setAdmitFailVisible}
        type="warn"
        title="退出失败"
        desc={noAdmitDesc}
        onOk={() => {
          log.addLog(`quit-${admitType}-admit-reject-popup-ok-click`, 'success');
        }}
        okText="我知道了"
      />
      {/* 消费者退出确认 */}
      <InterestConfirm
        visible={quitConfirmVisible}
        quitRetainPopupType={quitPopType}
        setVisible={setQuitConfirmVisible}
        onConfirm={handleConfirmQuit}
      />
      {/* 红包弹窗 */}
      <RedEnvelopeConfirm
        visible={redEnvelopeVisible}
        redEnvelopeInfo={redEnvelopeInfo}
        setVisible={setRedEnvelopeVisible}
        onQuitBtn={handleRedEnvelopeBtn}
        onReceiveBtn={handleReceiveEnvelopeBtn}
      />
      {/* 退出原因 */}
      <QuitFormPopup
        visible={quitFormVisible}
        setVisible={setQuitFormVisible}
        onConfirm={handleSubmitQuitForm}
      />
      <PartSetting
        visible={showPartSetting}
        setVisible={setShowPartSetting}
        giveType={renderContent?.giveType}
        onChangeSetting={handleOnPartChange}
      />
      <BasePopup
        visible={showPartServiceData}
        setVisible={setShowPartServiceData}
        title={'温馨提示'}
        desc={'因您选择部分开通退货宝权益，目前有部分消费者在您店铺购物未提供退货宝权益，如您想更改为全店开通，请在退货宝-更多-服务设置处进行修改'}
        descType={'full'}
      />
      <FixedRenewalTrail
        visible={showFixedRenewalTrail}
        setVisible={setShowFixedRenewalTrail}
        title={'续签年框详情'}
        fixedRenewalData={fixedRenewalData}
      />
    </div>
  );
};

export const pageConfig = definePageConfig(() => ({
  title: '退货宝',
  spm: {
    spmB: 'BYF_MERCHANT_UNIAPP-detail',
  },
}));

export default Detail;
