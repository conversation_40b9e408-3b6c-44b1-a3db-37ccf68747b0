/**
* @file PromotionDialog
* @date 2025-02-24
* <AUTHOR>
*/

import { useEffect, useState } from 'react';
import styles from './styles.module.css';
import { queryDelivery } from '@/api/promotion';
import { get, defaultTo } from 'lodash-es';
import dayjs from 'dayjs';
import { log } from '@ali/iec-dtao-utils';
import { ByfAppHomePoplayer, CLOSE_ICON } from '@/lib/constant';
import { Mask } from 'antd-mobile';

interface PromotionDialogProps {
  positionCode?: string;
}

const PromotionDialog = (props: PromotionDialogProps) => {
  // 弹窗属性
  const [deliveryPopupInfo, setDeliveryPopupInfo] = useState<any>({
    visible: false,
    materialType: '',
    backgroundImg: '',
    actionUrl: '',
    ugTrack: '',
  });

  // 疲劳度判断
  const fatigueJudgement = (params) => {
    // 获取疲劳度配置
    const { periodAmount, frequency, times, totalTimes, itemId, materialType, positionCode } = params;
    try {
      // localStorage的key
      const localStorageKey = `${positionCode}-${itemId}-${materialType}`;
      const fatigueConfigStr = localStorage.getItem(localStorageKey);
      if (!fatigueConfigStr) {
        // 如果没有设置过疲劳度缓存,说明第一次来
        // 获取周期到期的时间（自然日）
        const periodEnd = dayjs().add(periodAmount, frequency).startOf(frequency).valueOf();
        // 周期内疲劳度
        const currentPeriodCount = times - 1;
        // 总疲劳度
        const totalCount = totalTimes - 1;
        // 设置缓存
        localStorage.setItem(localStorageKey, JSON.stringify({ periodEnd, currentPeriodCount, totalCount }));
        // 设置为可见
        return true;
      } else {
        // 如果设置过疲劳度
        const fatigueConfig = JSON.parse(fatigueConfigStr);
        const { periodEnd, currentPeriodCount, totalCount } = fatigueConfig;

        if (dayjs().valueOf() < periodEnd) {
          // 在当前周期内
          if (currentPeriodCount > 0 && totalCount > 0) {
            // 如果周期内还有次数切总疲劳度还有次数，周期内有效次数-1，总疲劳度-1
            localStorage.setItem(localStorageKey, JSON.stringify({ periodEnd, currentPeriodCount: currentPeriodCount - 1, totalCount: totalCount - 1 }));
            return true;
          } else {
            return false;
          }
        } else if (totalCount > 0) {
          // 如果已经不在上一周期内，重新开启一个新周期
          // 如果还有总疲劳度剩余次数
          // 获取周期到期的时间
          const newPeriodEnd = dayjs().add(periodAmount, frequency).startOf(frequency).valueOf();
          // 设置缓存
          localStorage.setItem(localStorageKey, JSON.stringify({ periodEnd: newPeriodEnd, currentPeriodCount: times - 1, totalCount: totalCount - 1 }));
          // 设置为可见
          return true;
        } else {
          return false;
        }
      }
    } catch (error) {
      return false;
    }
  };


  const handleOnGetPopupInfo = async ({ positionCode }) => {
    try {
      const marketRes = await queryDelivery({ positionCode: ByfAppHomePoplayer });
      const { content: { items } } = marketRes;
      if (!items?.length) {
        return null;
      }
      // 获取第一个元素
      const currentItem = items.shift();
      // 获取疲劳度设置
      const fatitueStr = get(currentItem, 'fatigue');
      // 获取活动项id
      const itemId = get(currentItem, 'itemId');
      // 获取track
      const ugTrack = get(currentItem, 'ugTrack');
      const fatigue = fatitueStr ? JSON.parse(fatitueStr) : null;
      // 周期个数
      const periodAmount = defaultTo(Number(get(fatigue, 'periodAmount')), 1);
      // 周期
      const frequency = defaultTo(get(fatigue, 'frequency'), 'years');
      // 周期内疲劳度
      const times = defaultTo(Number(get(fatigue, 'times')), 1);
      // 总疲劳度
      const totalTimes = defaultTo(Number(get(fatigue, 'totalTimes')), 1);
      // 物料类型
      const materialType = get(currentItem, 'bizType');
      // 是否展示弹窗
      const canShow = fatigueJudgement({ periodAmount, frequency, times, totalTimes, itemId, materialType, positionCode });
      if (canShow) {
        log.addLog('byf-promotion-popup-show', 'success', { ugTrack });
        // 背景图片
        const backgroundImg = get(currentItem, 'imageUrl');
        // 跳转地址
        const actionUrl = materialType === 'ADVERTISING' ? get(currentItem, 'actionUrl') : null;
        setDeliveryPopupInfo({
          visible: true,
          materialType,
          backgroundImg,
          actionUrl,
          ugTrack,
        });
      }
      return null;
    } catch (error) {
      return null;
    }
  };

  const renderPopup = () => {
    const { materialType, backgroundImg, actionUrl, ugTrack } = deliveryPopupInfo;
    if (!deliveryPopupInfo?.visible) {
      return <></>;
    }
    switch (materialType) {
      case 'ADVERTISING':
        return (
          <div
            className={styles['advertising-popup']}
          >
            <img
              onClick={() => {
                log.addLog(`byf-detail-h5-${materialType}-popup-btn`, 'success', { ugTrack });
                window.open(actionUrl, '_blank');
              }}
              src={backgroundImg}
              className={styles['advertising-img']}
              alt=""
            />
            <img
              src={CLOSE_ICON}
              className={styles['close-icon']}
              alt=""
              onClick={() => {
                // 广告类弹窗关闭打点
                log.addLog(`byf-detail-h5-${materialType}-popup-close`, 'success', { ugTrack });
                setDeliveryPopupInfo({
                  visible: false,
                });
              }}
            />
          </div>
        );
      default:
        return <></>;
    }
  };

  useEffect(() => {
    handleOnGetPopupInfo({ positionCode: props?.positionCode });
  }, []);

  return (
    <>
      {deliveryPopupInfo?.visible && (
        <Mask
          className={styles['delivery-overlay']}
          visible={deliveryPopupInfo.visible}
          onMaskClick={() => {
            // 弹窗关闭打点
            log.addLog('byf-promotion-mask-popup-close', 'success', { ugTrack: deliveryPopupInfo?.ugTrack });
            setDeliveryPopupInfo({
              visible: false,
            });
          }}
        >
          {renderPopup()}
        </Mask>
      )}
    </>
  );
};

export default PromotionDialog;
