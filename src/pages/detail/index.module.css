.header {
  display: flex;
  align-items: flex-end;
  background: linear-gradient(180deg, #f3f5ff 0%, #f7f8fd 100%);
  padding: 42rpx 32rpx 26rpx 32rpx;
  font-size: 24rpx;
  font-weight: normal;
  line-height: 36rpx;
  gap: 12rpx;
}

.headerIcon {
  height: 56rpx;
}

.headerStatus {
  border-radius: 12rpx;
  padding-left: 12rpx;
  padding-right: 12rpx;
}

.exitContainer {
  background: white;
  display: flex;
  justify-content: center;
  font-size: 26rpx;
  font-weight: 500;
  line-height: 40rpx;
  padding: 16rpx 0;
}

.disable {
  pointer-events: none;
  color: #ccc;
}

.linkContainer {
  display: flex;
  justify-content: center;
  margin-top: 24rpx;
  margin-bottom: calc(24rpx + var(--safe-area-inset-bottom, 0px));
  font-size: 26rpx;
  line-height: 40rpx;

  a {
    color: var(--text-link);
  }
}

.dataContainer {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  gap: 12rpx;
}

.dataContent {
  display: flex;
  flex-direction: column;
  justify-content: center;
  width: 310rpx;
  height: 120rpx;
  border-radius: 12rpx;
  background: #f7f8fa;
  padding: 16rpx;
}

.dataTitle {
  font-size: 26rpx;
  font-weight: normal;
  line-height: 40rpx;
  color: var(--text-secondary);
  white-space: nowrap;
  display: flex;
  flex-direction: row;
  align-items: center;
}

.dataNumTag {
  display: flex;
  flex-direction: row;
  font-size: 26rpx;
  font-weight: normal;
  color: #3d5eff;
  line-height: 36rpx;
}

.dataNum {
  font-family: AlibabaFontMd;
  font-size: 36rpx;
  font-weight: 500;

  span {
    font-size: 24rpx;
    font-weight: normal;
  }
}

.showUpPanelButton {
  display: flex;
  flex-direction: row;
  justify-content: center;
  color: #3d5eff;
  font-size: 26rpx;
  margin-top: 20rpx;
}

.btnViewPanel {
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
}

.btnViewDown {
  width: 26rpx;
  height: 26rpx;
  background-image: url("https://gw.alicdn.com/imgextra/i4/O1CN01wuORWo1Ea8PG2krqR_!!6000000000367-55-tps-26-26.svg");
  background-size: 26rpx 26rpx;
}

.btnViewUp {
  width: 26rpx;
  height: 26rpx;
  background-image: url("https://gw.alicdn.com/imgextra/i3/O1CN01JYTgpp1krbDMJW9gV_!!6000000004737-55-tps-26-26.svg");
  background-size: 26rpx 26rpx;
}

.promotionDialog {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  margin-top: 300rpx;
  gap: 72rpx;
  .promotionDialog-img {
    display: flex;
    flex-direction: column;
    width: 590rpx;
    height: 660rpx;
    background-size: 590rpx 660rpx;
    background-image: url("https://gw.alicdn.com/imgextra/i1/O1CN01ZRB6nJ249RKJ5fZcD_!!6000000007348-2-tps-1180-1320.png");
  }
  .close-btn-icon {
    display: flex;
    width: 72rpx;
    height: 72rpx;
    background-size: 72rpx 72rpx;
    background-image: url("https://gw.alicdn.com/imgextra/i1/O1CN01xBvzLY1DlIQYnnalp_!!6000000000256-2-tps-144-144.png");
  }
}

.errorToast {
  width: 152px;
}

.fixed-renewal-container {
  margin-top: -14rpx;
  margin-bottom: 20rpx;
}

.noticeBarFixedRenewalContent {
  display: flex;
  flex-direction: row;
  margin-bottom: 20rpx;
  width: 600rpx;
  height: 20rpx;
  line-height: 40rpx;
  justify-content: space-between;
}

.noticeBarIcon {
  height: 36rpx;
  width: 36rpx;
}

:global(.adm-notice-bar) {
  border: none;
  border-radius: 12rpx;
}