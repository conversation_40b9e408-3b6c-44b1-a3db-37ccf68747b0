import { useEffect } from 'react';
import { routeByUserStatus } from '@/lib/route';
import { getUrlParams } from '@/lib/params';
import { log } from '@ali/iec-dtao-utils';
import { SpinLoading } from 'antd-mobile';
import { definePageConfig } from 'ice';

const DefaultPage = () => {
  useEffect(() => {
    const traceLog = getUrlParams(window.location.href, 'traceLog');
    log.addLog('byf-from', 'success', { from: traceLog ?? null });
    routeByUserStatus();
  });

  return (
    <div className="base">
      <div className="byf-loading">
        <SpinLoading />
      </div>
    </div>
  );
};

export const pageConfig = definePageConfig(() => ({
  title: '退货宝',
  spm: {
    spmB: 'BYF_MERCHANT_UNIAPP-home',
  },
}));

export default DefaultPage;
