import { useEffect } from 'react';
import { routeByUserStatus } from '@/lib/route';
import { getUrlParams } from '@/lib/params';
import { log } from '@alife/dtao-iec-spm-log';
import { SpinLoading } from 'antd-mobile';
import { definePageConfig } from 'ice';
import { handleAemLoad } from '@/lib/ameLog';

const DefaultPage = () => {
  useEffect(() => {
    handleAemLoad();
    log.reportInit('a360n.BYF_MERCHANT_UNIAPP-home');
    const traceLog = getUrlParams(window.location.href, 'traceLog');
    log.addLog('byf-from', 'success', { from: traceLog ?? null });
    routeByUserStatus();
  });

  return (
    <div className="base">
      <div className="byf-loading">
        <SpinLoading />
      </div>
    </div>
  );
};

export const pageConfig = definePageConfig(() => ({
  title: '退货宝',
}));

export default DefaultPage;
