.coupon {
  padding: 0;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.notice{
  min-height: 56px; /* 改为最小高度，允许内容撑开 */
  display: flex;
  gap: 6px; /* 12rpx转换为6px */
  z-index: 3;
  padding: 0; /* 确保没有额外的内边距 */
}

.couponList {
  flex: 1;
  background-color: #f5f5f5;
  padding-bottom: 20px;
}

.couponCardList {
  padding: 8px 0;
}

.emptyState {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  text-align: center;
}

.emptyText {
  font-size: 16px;
  color: #999;
  margin-top: 16px;
}