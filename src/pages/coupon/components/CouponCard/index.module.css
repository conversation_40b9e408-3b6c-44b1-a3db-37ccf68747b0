.couponCard{
  position: relative;
  display: flex;
  flex-direction: column;
  padding: 32rpx;
  gap: 24rpx;
  border-radius: 12rpx;
  background: linear-gradient(135deg, #fff5f0 0%, #fff2e8 100%);
  margin: 16rpx 32rpx;
  overflow: hidden;
}

/* 状态角标 */
.couponStatus {
  position: absolute;
  top: 0;
  right: 0;
  background: #ff6b35;
  color: white;
  font-size: 20rpx;
  padding: 8rpx 16rpx;
  border-radius: 0 12rpx 0 12rpx;
  font-weight: 500;
  z-index: 2;
}



/* 主要内容区域 */
.couponContent {
  display: flex;
  align-items: flex-start;
  gap: 24rpx;
  flex: 1;
}

/* 价格容器 */
.priceContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
  min-width: 120rpx;
  padding-right: 24rpx;
  border-right: 2rpx dashed #ddd;
}

/* 价格文本 */
.priceText {
  font-size: 48rpx;
  font-weight: 600;
  color: #ff6b35;
  line-height: 1;
  margin-bottom: 8rpx;
}



/* 价格单位 */
.priceUnit {
  font-size: 24rpx;
  color: #ff6b35;
  font-weight: 400;
}

/* 标题容器 */
.titleContainer {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

/* 优惠券标签 */
.tag {
  background: rgba(255, 107, 53, 0.1);
  color: #ff6b35;
  font-size: 20rpx;
  padding: 4rpx 12rpx;
  border-radius: 20rpx;
  font-weight: 500;
  align-self: flex-start;
}



/* 标题 */
.title {
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
  line-height: 1.3;
  margin-bottom: 8rpx;
}



/* 日期范围 */
.dateRange {
  font-size: 24rpx;
  color: #999;
  line-height: 1.2;
}



/* 分割线 */
.divide {
  height: 2rpx;
  background: #e0e0e0;
  margin: 0 -32rpx;
  margin-bottom: 0;
}

/* 使用规则 */
.rule {
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 24rpx;
  color: #666;
  line-height: 1.3;
  position: relative;
}

.rule svg {
  width: 12rpx;
  height: 20rpx;
  flex-shrink: 0;
}

/* 平台优惠样式 */
.couponCard.platform {
  background: linear-gradient(135deg, #fff5f0 0%, #fff2e8 100%);
}

.couponCard.platform .priceText {
  color: #ff6b35;
}

.couponCard.platform .tag {
  background: rgba(255, 107, 53, 0.1);
  color: #ff6b35;
}

/* 活动优惠样式 */
.couponCard.activity {
  background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
}

.couponCard.activity .priceText {
  color: #0ea5e9;
}

.couponCard.activity .tag {
  background: rgba(14, 165, 233, 0.1);
  color: #0ea5e9;
}

.couponCard.activity .couponStatus {
  background: #0ea5e9;
}