.couponCard {
  position: relative;
  display: flex;
  flex-direction: column;
  padding: 32rpx;
  gap: 24rpx;
  border-radius: 12rpx;
  margin: 16rpx 32rpx;
  overflow: hidden;
}

/* 状态角标 */
.couponStatus {
  position: absolute;
  top: 0;
  right: 0;
  color: white;
  font-size: 20rpx;
  padding: 2rpx 8rpx;
  border-radius: 0 12rpx 0 12rpx;
  font-weight: 500;
  z-index: 2;
}

.statusText {
  font-size: 20rpx;
  font-weight: normal;
  line-height: normal;
  letter-spacing: normal;
  color: #fff;
}

/* 主要内容区域 */
.couponContent {
  display: flex;
  align-items: flex-start;
  gap: 24rpx;
  flex: 1;
}

/* 价格容器 */
.priceContainer {
  width: 120rpx;
  display: flex;
  flex-direction: row;
  align-items: center;
  margin: auto;
  justify-content: center;
}

/* 价格文本 */
.priceText {
  font-size: 40rpx;
  font-weight: 600;
  line-height: 1;
  margin-bottom: 8rpx;
  font-family: "AlibabaFontMd";
  display: flex;
  align-items: baseline;
}

/* 价格整数部分 - 大字体 */
.priceTextLarge {
  font-size: 48rpx;
  font-weight: 500;
  font-family: "AlibabaFontMd";
}

/* 价格小数部分 - 小字体 */
.priceTextSmall {
  font-size: 35rpx;
  font-weight: 500;
  font-family: "AlibabaFontMd";
}

/* 价格单位 */
.priceUnit {
  margin-top: 3rpx;
  font-size: 28rpx;
  font-weight: 500;
}

/* 标题容器 */
.titleContainer {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.topArea {
  display: flex;
  flex-direction: row;
  align-items: center;
  padding: 0rpx;
  gap: 8rpx;
  margin-bottom: 15rpx;
}

/* 优惠券标签 */
.tag {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 2rpx 8rpx;
  border-radius: 8rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
}
.tagText {
  font-size: 20rpx;
  font-weight: normal;
  line-height: normal;
  letter-spacing: normal;
}

/* 标题 */
.title {
  font-size: 26rpx;
  font-weight: 600;
  line-height: 26rpx;
  letter-spacing: normal;
  color: #11192d;
}



/* 日期范围 */
.dateRange {
  font-size: 24rpx;
  font-weight: normal;
  line-height: normal;
  letter-spacing: normal;
  color: #666;
}



/* 分割线 */
.divide {
  height: 0rpx;
  align-self: stretch;
  z-index: 1;
  border-top: 1rpx dashed #ffb999;
}

/* 使用规则 */
.rule {
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 24rpx;
  font-weight: normal;
  color: #666;
  line-height: 1.3;
  position: relative;
  font-family: "AlibabaFontMd";
}

.ruleIcon {
  width: 24rpx;
  height: 24rpx;
  margin-bottom: 8rpx;
}
