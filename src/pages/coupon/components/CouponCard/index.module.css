.couponCard {
  position: relative;
  margin: 8px 16px;
  border-radius: 8px;
  overflow: hidden;
  cursor: pointer;
  transition: transform 0.2s ease;
}

.couponCard:active {
  transform: scale(0.98);
}

/* 平台优惠背景色 */
.couponCard.platform {
  background: linear-gradient(135deg, #fff5f0 0%, #fff2e8 100%);
  border: 1px solid #ffe7d3;
}

/* 活动优惠背景色 */
.couponCard.activity {
  background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
  border: 1px solid #bae6fd;
}

/* 已使用状态 */
.couponCard.used {
  background: #f5f5f5 !important;
  border: 1px solid #e0e0e0 !important;
}

.couponCard.used .discountValue,
.couponCard.used .title,
.couponCard.used .description,
.couponCard.used .timeInfo {
  color: #999 !important;
}

/* 状态角标 */
.statusBadge {
  position: absolute;
  top: 0;
  right: 0;
  background: #ff6b35;
  color: white;
  font-size: 10px;
  padding: 2px 8px;
  border-radius: 0 8px 0 8px;
  font-weight: 500;
  z-index: 2;
}

/* 卡片内容 */
.cardContent {
  display: flex;
  align-items: center;
  padding: 16px;
  gap: 12px;
}

/* 左侧优惠信息区域 */
.discountSection {
  display: flex;
  flex-direction: column;
  align-items: center;
  min-width: 80px;
  padding-right: 12px;
  border-right: 1px dashed #ddd;
}

.discountValue {
  font-size: 24px;
  font-weight: 600;
  color: #ff6b35;
  line-height: 1;
  margin-bottom: 4px;
  display: flex;
  align-items: baseline;
}

.discountUnit {
  font-size: 14px;
  font-weight: 400;
  margin-left: 2px;
}

.discountType {
  font-size: 12px;
  color: #666;
  background: rgba(255, 107, 53, 0.1);
  padding: 2px 6px;
  border-radius: 10px;
  white-space: nowrap;
}

/* 右侧信息区域 */
.infoSection {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.title {
  font-size: 16px;
  font-weight: 500;
  color: #333;
  line-height: 1.3;
  margin-bottom: 2px;
}

.description {
  font-size: 14px;
  color: #666;
  line-height: 1.3;
  margin-bottom: 4px;
}

.timeInfo {
  font-size: 12px;
  color: #999;
  line-height: 1.2;
}

/* 箭头图标 */
.arrow {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
  flex-shrink: 0;
}

/* 平台优惠特殊样式 */
.platform .discountValue {
  color: #ff6b35;
}

.platform .discountType {
  background: rgba(255, 107, 53, 0.1);
  color: #ff6b35;
}

/* 活动优惠特殊样式 */
.activity .discountValue {
  color: #0ea5e9;
}

.activity .discountType {
  background: rgba(14, 165, 233, 0.1);
  color: #0ea5e9;
}

/* 响应式调整 */
@media (max-width: 375px) {
  .cardContent {
    padding: 12px;
    gap: 8px;
  }
  
  .discountSection {
    min-width: 70px;
    padding-right: 8px;
  }
  
  .discountValue {
    font-size: 20px;
  }
  
  .title {
    font-size: 15px;
  }
}
