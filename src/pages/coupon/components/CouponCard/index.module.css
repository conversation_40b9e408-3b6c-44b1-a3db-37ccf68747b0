/* 借鉴PC端的卡片基础样式 */
.couponCard {
  position: relative;
  margin: 8px 16px;
  border-radius: 8px;
  overflow: hidden;
  cursor: pointer;
  transition: transform 0.2s ease;
  background: #fff;
  border: 1px solid #e0e0e0;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.couponCard:active {
  transform: scale(0.98);
}

/* 平台优惠背景色 */
.couponCard.platform {
  background: linear-gradient(135deg, #fff5f0 0%, #fff2e8 100%);
  border: 1px solid #ffe7d3;
}

/* 活动优惠背景色 */
.couponCard.activity {
  background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
  border: 1px solid #bae6fd;
}

/* 借鉴PC端的状态角标样式 */
.couponCardCorner {
  position: absolute;
  top: 0;
  right: 0;
  display: flex;
  align-items: center;
  background: #ff6b35;
  color: white;
  font-size: 10px;
  padding: 4px 8px;
  border-radius: 0 8px 0 8px;
  font-weight: 500;
  z-index: 2;
}

.couponIcon {
  width: 12px;
  height: 12px;
  background: white;
  border-radius: 50%;
  margin-right: 4px;
}

.couponUsedIcon {
  width: 16px;
  height: 16px;
  background: #999;
  border-radius: 2px;
}

.couponStatus {
  font-size: 10px;
  color: white;
}

/* 借鉴PC端的优惠券标签 */
.couponCardTag {
  position: absolute;
  top: 8px;
  left: 8px;
  background: rgba(255, 107, 53, 0.1);
  color: #ff6b35;
  font-size: 10px;
  padding: 2px 6px;
  border-radius: 10px;
  font-weight: 500;
}

/* 借鉴PC端的主要内容区域 */
.couponCardContent {
  display: flex;
  align-items: center;
  padding: 16px;
  gap: 12px;
}

/* 借鉴PC端的金额显示区域 */
.couponCardContentAmount {
  display: flex;
  flex-direction: column;
  align-items: center;
  min-width: 80px;
  padding-right: 12px;
  border-right: 1px dashed #ddd;
  font-size: 24px;
  font-weight: 600;
  color: #ff6b35;
  line-height: 1;
}

.couponCardContentAmount span {
  font-size: 14px;
  font-weight: 400;
  margin-left: 2px;
}

/* 借鉴PC端的详情区域 */
.couponCardContentDetail {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.couponCardContentDetailTitle {
  font-size: 16px;
  font-weight: 500;
  color: #333;
  line-height: 1.3;
  margin-bottom: 2px;
}

.couponCardContentDetailRange {
  font-size: 12px;
  color: #999;
  line-height: 1.2;
  margin-bottom: 2px;
}

.couponCardContentDetailExtra {
  font-size: 12px;
  color: #666;
  line-height: 1.2;
}

/* 借鉴PC端的分割线 */
.couponCardSplitLine {
  height: 1px;
  background: #e0e0e0;
  margin: 0 16px;
}

/* 借鉴PC端的规则区域 */
.couponCardContentRule {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 16px;
  font-size: 12px;
  color: #666;
  line-height: 1.3;
}

.arrowIcon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 16px;
  height: 16px;
  flex-shrink: 0;
}

/* 平台优惠特殊样式 */
.platform .couponCardContentAmount {
  color: #ff6b35;
}

.platform .couponCardTag {
  background: rgba(255, 107, 53, 0.1);
  color: #ff6b35;
}

/* 活动优惠特殊样式 */
.activity .couponCardContentAmount {
  color: #0ea5e9;
}

.activity .couponCardTag {
  background: rgba(14, 165, 233, 0.1);
  color: #0ea5e9;
}

/* 响应式调整 */
@media (max-width: 375px) {
  .couponCardContent {
    padding: 12px;
    gap: 8px;
  }

  .couponCardContentAmount {
    min-width: 70px;
    padding-right: 8px;
    font-size: 20px;
  }

  .couponCardContentDetailTitle {
    font-size: 15px;
  }
}
