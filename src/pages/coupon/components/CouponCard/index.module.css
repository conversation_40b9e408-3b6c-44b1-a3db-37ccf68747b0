.couponCard{
  position: relative;
  display: flex;
  flex-direction: column;
  padding: 32rpx;
  gap: 24rpx;
  border-radius: 12rpx;
  margin: 16rpx 32rpx;
  overflow: hidden;
}

/* 状态角标 */
.couponStatus {
  position: absolute;
  top: 0;
  right: 0;
  color: white;
  font-size: 20rpx;
  padding: 8rpx 16rpx;
  border-radius: 0 12rpx 0 12rpx;
  font-weight: 500;
  z-index: 2;
}



/* 主要内容区域 */
.couponContent {
  display: flex;
  align-items: flex-start;
  gap: 24rpx;
  flex: 1;
}

/* 价格容器 */
.priceContainer {
  display: flex;
  flex-direction: row;
  align-items: center;
  margin:auto;
  justify-content: center;
  padding-right: 24rpx;
  border-right: 2rpx dashed #ddd;
}

/* 价格文本 */
.priceText {
  font-size: 40rpx;
  font-weight: 600;
  line-height: 1;
  margin-bottom: 8rpx;
}



/* 价格单位 */
.priceUnit {
  font-size: 26rpx;
  font-weight: 400;
}

/* 标题容器 */
.titleContainer {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.topArea {
  display: flex;
  flex-direction: row;
  align-items: center;
  padding:0rpx;
}

/* 优惠券标签 */
.tag {
  font-size: 20rpx;
  padding: 4rpx 12rpx;
  border-radius: 20rpx;
  font-weight: 500;
  align-self: flex-start;
}



/* 标题 */
.title {
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
  line-height: 1.3;
  margin-bottom: 8rpx;
}



/* 日期范围 */
.dateRange {
  font-size: 24rpx;
  color: #999;
  line-height: 1.2;
}



/* 分割线 */
.divide {
  height: 2rpx;
  background: #e0e0e0;
  margin: 0 -32rpx;
  margin-bottom: 0;
}

/* 使用规则 */
.rule {
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 24rpx;
  color: #666;
  line-height: 1.3;
  position: relative;
}

.rule svg {
  width: 12rpx;
  height: 20rpx;
  flex-shrink: 0;
}

