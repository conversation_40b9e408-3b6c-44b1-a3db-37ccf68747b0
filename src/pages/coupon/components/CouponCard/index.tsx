import React from 'react';
import styles from './index.module.css';

// 借鉴PC端的状态定义
const COUPON_STATUS = {
  COUPON_AVAILABLE: '待生效',
  COUPON_USING: '生效中',
  COUPON_USED: '已使用',
  COUPON_INVALID: '已失效',
  COUPON_EXPIRE: '已过期',
};

// 借鉴PC端的数据接口
interface CouponCardData {
  id: string;
  label: string;
  price: {
    priceText: string;
    priceUnit: string | null;
  };
  title: string;
  dateRange: string;
  limit: string;
  rule: string;
  status: string;
  subsidyType: string;
}

// 借鉴PC端的背景样式控制
const CouponCardBg = {
  // 待生效
  COUPON_AVAILABLE: {},
  // 生效中
  COUPON_USING: {},
  // 已使用
  COUPON_USED: {
    opacity: 0.7,
  },
  // 已失效
  COUPON_EXPIRE: {
    opacity: 0.7,
    filter: 'grayscale(100%)',
  },
  // 已失效
  COUPON_INVALID: {
    opacity: 0.7,
    filter: 'grayscale(100%)',
  },
};

export interface CouponCardProps {
  /** 优惠券数据 */
  data: CouponCardData;
  /** 点击事件 */
  onClick?: (id: string) => void;
}

const CouponCard: React.FC<CouponCardProps> = ({
  data,
  onClick
}) => {
  // 借鉴PC端的类型判断逻辑
  const getCardType = () => {
    if (data.subsidyType === 'PLATFORM_SUBSIDY') return 'platform';
    return 'activity';
  };

  const type = getCardType();

  return (
    <div
      className={`${styles.couponCard} ${styles[type]}`}
      style={{
        opacity: CouponCardBg[data.status]?.opacity ?? 1,
        filter: CouponCardBg[data.status]?.filter ?? 'none',
      }}
      onClick={() => onClick?.(data.id)}
    >
      {/* 状态角标 - 借鉴PC端逻辑 */}
      <div className={styles.couponCardCorner}>
        {['COUPON_USING'].indexOf(data.status) > -1 ? (
          <>
            <div className={styles.couponIcon} />
            <span className={styles.couponStatus}>{COUPON_STATUS[data.status]}</span>
          </>
        ) : (
          <div className={styles.couponUsedIcon} />
        )}
      </div>

      {/* 优惠券类型标签 */}
      <div className={styles.couponCardTag}>
        <span>{data.label}</span>
      </div>

      {/* 主要内容区域 */}
      <div className={styles.couponCardContent}>
        {/* 优惠金额 */}
        <div className={styles.couponCardContentAmount}>
          {data.price?.priceText}
          {data.price?.priceUnit && <span>{data.price?.priceUnit}</span>}
        </div>

        {/* 详细信息 */}
        <div
          className={styles.couponCardContentDetail}
          style={{
            marginLeft: data?.price?.priceUnit ? 22 : 31,
          }}
        >
          <div className={styles.couponCardContentDetailTitle}>
            {data.title}
          </div>
          <div className={styles.couponCardContentDetailRange}>
            {data.dateRange}
          </div>
          <div className={styles.couponCardContentDetailExtra}>
            {data.limit}
          </div>
        </div>
      </div>

      {/* 分割线 */}
      <div className={styles.couponCardSplitLine} />

      {/* 使用规则 */}
      <div className={styles.couponCardContentRule}>
        {data.rule}
        <div className={styles.arrowIcon}>
          <svg width="6" height="10" viewBox="0 0 6 10" fill="none">
            <path
              d="M1 1L5 5L1 9"
              stroke="#999"
              strokeWidth="1.5"
              strokeLinecap="round"
              strokeLinejoin="round"
            />
          </svg>
        </div>
      </div>
    </div>
  );
};

export default CouponCard;
