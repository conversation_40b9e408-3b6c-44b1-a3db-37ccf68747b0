import React from 'react';
import styles from './index.module.css';


export interface CouponCardProps {
  id: string;
  label: string;
  price: {
    priceText: string;
    priceUnit: string | null;
  };
  title: string;
  dateRange: string;
  limit: string;
  rule: string;
  status: string;
  subsidyType: string;
  hidden: boolean;
}
const CouponCardBg = {
  // 待生效
  COUPON_AVAILABLE: {
  },
  // 生效中
  COUPON_USING: {
  },
  // 已使用
  COUPON_USED: {
    opacity: 0.7,
  },
  // 已失效
  COUPON_EXPIRE: {
    opacity: 0.7,
    filter: 'grayscale(100%)',
  },
  // 已失效
  COUPON_INVALID: {
    opacity: 0.7,
    filter: 'grayscale(100%)',
  },
};
const COUPON_STATUS = {
  COUPON_AVAILABLE: '待生效',
  COUPON_USING: '生效中',
  COUPON_USED: '已使用',
  COUPON_INVALID: '已失效',
  COUPON_EXPIRE: '已过期',
};

const CouponCard = ({data,onClick}:{data:CouponCardProps;onClick: (id: string) => void;}) => {
  // 优惠券类型判断逻辑
  const getCardType = () => {
    if (data.subsidyType === 'PLATFORM_SUBSIDY') return 'platform';
    return 'activity';
  };

  const type = getCardType();

  return (
   <div className={styles.couponCard}>
    
   </div>
  );
};

export default CouponCard;
