import React from 'react';
import styles from './index.module.css';

export interface CouponCardProps {
  /** 原始数据 */
  data: any;
  /** 点击事件 */
  onClick?: () => void;
}

const CouponCard: React.FC<CouponCardProps> = ({
  data,
  onClick
}) => {
  // 从原始数据中提取并转换所需信息
  const getCardType = () => {
    if (data.subsidyType === 'PLATFORM_SUBSIDY') return 'platform';
    return 'activity';
  };

  const getCardStatus = () => {
    if (data.status === 'COUPON_USING') return 'using';
    if (data.status === 'COUPON_USED') return 'used';
    return 'available';
  };

  const getDiscountInfo = () => {
    const priceInfo = data.price;
    if (!priceInfo?.priceText || priceInfo.priceText === '-') {
      return {
        type: 'free' as const,
        value: '免费',
        unit: undefined
      };
    }
    if (priceInfo.priceUnit === '折') {
      return {
        type: 'discount' as const,
        value: priceInfo.priceText,
        unit: '折'
      };
    }
    if (priceInfo.priceUnit === '元') {
      return {
        type: 'reduce' as const,
        value: priceInfo.priceText,
        unit: '元'
      };
    }
    return {
      type: 'free' as const,
      value: '免费',
      unit: undefined
    };
  };

  const parseTimeRange = () => {
    if (data.dateRange) {
      const [effectTime, expireTime] = data.dateRange.split('～');
      return {
        effectTime: effectTime?.trim() || '',
        expireTime: expireTime?.trim() || ''
      };
    }
    return {
      effectTime: '',
      expireTime: ''
    };
  };

  const type = getCardType();
  const status = getCardStatus();
  const title = data.title || '优惠券';
  const discount = getDiscountInfo();
  const description = data.rule || '满足条件即可使用';
  const label = data.label;
  const timeRange = parseTimeRange();
  const effectTime = timeRange.effectTime;
  const expireTime = timeRange.expireTime;
  // 获取状态标签文本
  const getStatusText = () => {
    switch (status) {
      case 'using':
        return '生效中';
      case 'available':
        return '待生效';
      case 'used':
        return '已使用';
      default:
        return '';
    }
  };

  // 获取优惠显示文本
  const getDiscountText = () => {
    switch (discount.type) {
      case 'free':
        return '免费';
      case 'discount':
        return `${discount.value}折`;
      case 'reduce':
        return `${discount.value}`;
      default:
        return discount.value;
    }
  };

  // 获取优惠单位
  const getDiscountUnit = () => {
    if (discount.unit) {
      return discount.unit;
    }
    return discount.type === 'reduce' ? '元' : '';
  };

  return (
    <div 
      className={`${styles.couponCard} ${styles[type]} ${styles[status]}`}
      onClick={onClick}
    >
      {/* 状态角标 */}
      {status === 'using' && (
        <div className={styles.statusBadge}>
          {getStatusText()}
        </div>
      )}

      <div className={styles.cardContent}>
        {/* 左侧优惠信息 */}
        <div className={styles.discountSection}>
          <div className={styles.discountValue}>
            {getDiscountText()}
            {getDiscountUnit() && (
              <span className={styles.discountUnit}>{getDiscountUnit()}</span>
            )}
          </div>
          <div className={styles.discountType}>
            {label || (type === 'platform' ? '平台优惠' : '活动优惠')}
          </div>
        </div>

        {/* 右侧详细信息 */}
        <div className={styles.infoSection}>
          <div className={styles.title}>{title}</div>
          <div className={styles.description}>{description}</div>
          <div className={styles.timeInfo}>
            {effectTime} - {expireTime}
          </div>
        </div>

        {/* 箭头图标 */}
        <div className={styles.arrow}>
          <svg width="6" height="10" viewBox="0 0 6 10" fill="none">
            <path 
              d="M1 1L5 5L1 9" 
              stroke="#999" 
              strokeWidth="1.5" 
              strokeLinecap="round" 
              strokeLinejoin="round"
            />
          </svg>
        </div>
      </div>
    </div>
  );
};

export default CouponCard;
