import React from 'react';
import styles from './index.module.css';

export interface CouponCardProps {
  /** 优惠券类型：平台优惠或活动优惠 */
  type: 'platform' | 'activity';
  /** 优惠券状态 */
  status: 'using' | 'available' | 'used';
  /** 优惠券标题 */
  title: string;
  /** 优惠信息 */
  discount: {
    /** 优惠类型：免费、折扣、减免 */
    type: 'free' | 'discount' | 'reduce';
    /** 优惠值 */
    value: string | number;
    /** 单位 */
    unit?: string;
  };
  /** 标题文案 */
  description: string;
  /** 生效时间 */
  effectTime: string;
  /** 失效时间 */
  expireTime: string;
  /** 优惠券标签（可选，用于显示具体类型如"行业优惠"） */
  label?: string;
  /** 点击事件 */
  onClick?: () => void;
}

const CouponCard: React.FC<CouponCardProps> = ({
  type,
  status,
  title,
  discount,
  description,
  effectTime,
  expireTime,
  label,
  onClick
}) => {
  // 获取状态标签文本
  const getStatusText = () => {
    switch (status) {
      case 'using':
        return '生效中';
      case 'available':
        return '待生效';
      case 'used':
        return '已使用';
      default:
        return '';
    }
  };

  // 获取优惠显示文本
  const getDiscountText = () => {
    switch (discount.type) {
      case 'free':
        return '免费';
      case 'discount':
        return `${discount.value}折`;
      case 'reduce':
        return `${discount.value}`;
      default:
        return discount.value;
    }
  };

  // 获取优惠单位
  const getDiscountUnit = () => {
    if (discount.unit) {
      return discount.unit;
    }
    return discount.type === 'reduce' ? '元' : '';
  };

  return (
    <div 
      className={`${styles.couponCard} ${styles[type]} ${styles[status]}`}
      onClick={onClick}
    >
      {/* 状态角标 */}
      {status === 'using' && (
        <div className={styles.statusBadge}>
          {getStatusText()}
        </div>
      )}

      <div className={styles.cardContent}>
        {/* 左侧优惠信息 */}
        <div className={styles.discountSection}>
          <div className={styles.discountValue}>
            {getDiscountText()}
            {getDiscountUnit() && (
              <span className={styles.discountUnit}>{getDiscountUnit()}</span>
            )}
          </div>
          <div className={styles.discountType}>
            {label || (type === 'platform' ? '平台优惠' : '活动优惠')}
          </div>
        </div>

        {/* 右侧详细信息 */}
        <div className={styles.infoSection}>
          <div className={styles.title}>{title}</div>
          <div className={styles.description}>{description}</div>
          <div className={styles.timeInfo}>
            {effectTime} - {expireTime}
          </div>
        </div>

        {/* 箭头图标 */}
        <div className={styles.arrow}>
          <svg width="6" height="10" viewBox="0 0 6 10" fill="none">
            <path 
              d="M1 1L5 5L1 9" 
              stroke="#999" 
              strokeWidth="1.5" 
              strokeLinecap="round" 
              strokeLinejoin="round"
            />
          </svg>
        </div>
      </div>
    </div>
  );
};

export default CouponCard;
