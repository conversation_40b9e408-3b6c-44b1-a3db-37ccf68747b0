import styles from './index.module.css';
import { RightOutline } from 'antd-mobile-icons';
// 优惠券样式配置常量
export const COUPON_STYLE_CONFIG = {
  // 平台优惠样式配置
  PLATFORM_SUBSIDY: {
    background: '#FFFFFF',
    priceColor: '#FF5000',
    tagBackground: '#FFF2E6',
    tagColor: '#FF5000',
    statusBackground: 'linear-gradient(90deg, #FFA881 2%, #FF5000 108%)',
  },
  // 活动优惠样式配置（包括行业优惠和活动优惠）
  PROMOTION_SUBSIDY: {
    background: 'linear-gradient(108deg, #FFEEE6 0%, #FFFFFF 22%, #FFFFFF 76%, #FFEEE6 100%)',
    priceColor: '#FF5000',
    tagBackground: 'linear-gradient(90deg, #FFA881 2%, #FF5000 108%)',
    tagColor: '#FFFFFF',
    statusBackground: 'linear-gradient(90deg, #FFA881 2%, #FF5000 108%)',
  },
  // 行业优惠使用与活动优惠相同的样式
  BIZ_SUBSIDY: {
    background: 'linear-gradient(108deg, #FFEEE6 0%, #FFFFFF 22%, #FFFFFF 76%, #FFEEE6 100%)',
    priceColor: '#FF5000',
    tagBackground: 'linear-gradient(90deg, #FFA881 2%, #FF5000 108%)',
    tagColor: '#FFFFFF',
    statusBackground: 'linear-gradient(90deg, #FFA881 2%, #FF5000 108%)',
  },
};
export const LIMIT_STYLE_CONFIG = {
  LIMITIED: {
    color: '#FF5000',
  },
  DEFAULT: {
    color: '#666666',
  },
};
export interface CouponCardProps {
  id: string;
  label: string;
  price: {
    priceText: string;
    priceUnit: string | null;
  };
  title: string;
  dateRange: string;
  limit: string;
  rule: string;
  status: string;
  subsidyType: string;
  hidden: boolean;
}
const CouponCardBg = {
  // 待生效
  COUPON_AVAILABLE: {
    opacity: 1,
    statusOpacity: 0.7,
  },
  // 生效中
  COUPON_USING: {
    opacity: 1,
  },
  // 已使用
  COUPON_USED: {
    opacity: 0.4,
  },
  // 已失效
  COUPON_EXPIRE: {
    opacity: 0.4,
    filter: 'grayscale(100%)',
  },
  // 已失效
  COUPON_INVALID: {
    opacity: 0.4,
    filter: 'grayscale(100%)',
  },
};
const COUPON_STATUS = {
  COUPON_AVAILABLE: '待生效',
  COUPON_USING: '生效中',
  COUPON_USED: '已使用',
  COUPON_INVALID: '已失效',
  COUPON_EXPIRE: '已过期',
};

const CouponCard = ({ data, onClick }: {data: CouponCardProps;onClick: (id: string) => void}) => {
  // 获取当前优惠券的样式配置
  const styleConfig = COUPON_STYLE_CONFIG[data.subsidyType] || COUPON_STYLE_CONFIG.PLATFORM_SUBSIDY;
  const limitStyleConfig = data.limit ? LIMIT_STYLE_CONFIG.LIMITIED : LIMIT_STYLE_CONFIG.DEFAULT;
  const ruleText = data.limit ? data.rule : '优惠详情';

  // 分割价格数字，突出整数部分
  const renderPriceWithHighlight = () => {
    const priceText = data.price?.priceText === '-' ? '免费' : data.price?.priceText;

    if (priceText === '免费') {
      return <span className={styles.priceTextSmall}>{priceText}</span>;
    }

    // 检查是否包含小数点
    if (priceText && priceText.includes('.')) {
      const [integerPart, decimalPart] = priceText.split('.');
      return (
        <>
          <span className={styles.priceTextLarge}>{integerPart}</span>
          <span className={styles.priceTextSmall}>.{decimalPart}</span>
        </>
      );
    }

    // 没有小数点的情况
    return <span className={styles.priceTextLarge}>{priceText}</span>;
  };

  return (
    <div
      className={styles.couponCard}
      style={{
        background: styleConfig.background,
        opacity: CouponCardBg[data.status]?.opacity ?? 1,
        filter: CouponCardBg[data.status]?.filter ?? 'none',
      }}
      onClick={() => onClick(data.id)}
    >
      {/* 状态角标 */}
      {data.status && (
      <div
        className={styles.couponStatus}
        style={{ background: styleConfig.statusBackground,
          opacity: CouponCardBg[data.status]?.statusOpacity ?? 1,
        }}
      >
        <div className={styles.statusText}> {COUPON_STATUS[data.status]}</div>
      </div>
      )}

      {/* 主要内容区域 */}
      <div className={styles.couponContent}>
        {/* 价格容器 */}
        <div className={styles.priceContainer}>
          <div
            className={styles.priceText}
            style={{ color: styleConfig.priceColor }}
          >
            {renderPriceWithHighlight()}
          </div>
          {data.price?.priceUnit && data.price?.priceText !== '-' && (
          <div
            className={styles.priceUnit}
            style={{ color: styleConfig.priceColor }}
          >
            {data.price?.priceUnit}
          </div>
          )}
        </div>
        {/* 标题容器 */}
        <div className={styles.titleContainer}>
          <div className={styles.topArea}>
            <div
              className={styles.tag}
              style={{ background: styleConfig.tagBackground }}
            >
              <div className={styles.tagText} style={{ color: styleConfig.tagColor }}>
                {data.label}
              </div>
            </div>
            <div className={styles.title}>{data.title}</div>
          </div>
          <div className={styles.dateRange}>{data.dateRange}</div>
        </div>
      </div>

      {/* 分割线 */}
      <div className={styles.divide} />
      {/* 使用规则 */}
      <div className={styles.rule}>
        <span style={{ color: limitStyleConfig.color }}>{ruleText}</span>
        <div
          className={styles.ruleIcon}
          onClick={() => {
            // 对包含#的ID进行URL编码
            const encodedId = encodeURIComponent(data.id);
            window.location.href = (`/couponDetail?id=${encodedId}`);
          }}
        ><RightOutline />
        </div>
      </div>
    </div>
  );
};

export default CouponCard;
