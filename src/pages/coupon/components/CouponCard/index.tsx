import React from 'react';
import styles from './index.module.css';
import { RightOutline } from 'antd-mobile-icons';

export interface CouponCardProps {
  id: string;
  label: string;
  price: {
    priceText: string;
    priceUnit: string | null;
  };
  title: string;
  dateRange: string;
  limit: string;
  rule: string;
  status: string;
  subsidyType: string;
  hidden: boolean;
}
const CouponCardBg = {
  // 待生效
  COUPON_AVAILABLE: {
  },
  // 生效中
  COUPON_USING: {
  },
  // 已使用
  COUPON_USED: {
    opacity: 0.7,
  },
  // 已失效
  COUPON_EXPIRE: {
    opacity: 0.7,
    filter: 'grayscale(100%)',
  },
  // 已失效
  COUPON_INVALID: {
    opacity: 0.7,
    filter: 'grayscale(100%)',
  },
};
const COUPON_STATUS = {
  COUPON_AVAILABLE: '待生效',
  COUPON_USING: '生效中',
  COUPON_USED: '已使用',
  COUPON_INVALID: '已失效',
  COUPON_EXPIRE: '已过期',
};

const CouponCard = ({data,onClick}:{data:CouponCardProps;onClick: (id: string) => void;}) => {
  // 优惠券类型判断逻辑
  const getCardType = () => {
    if (data.subsidyType === 'PLATFORM_SUBSIDY') return 'platform';
    return 'activity';
  };

  const type = getCardType();

  return (
   <div
     className={`${styles.couponCard} ${styles[type]}`}
     style={{
       opacity: CouponCardBg[data.status]?.opacity ?? 1,
       filter: CouponCardBg[data.status]?.filter ?? 'none',
     }}
     onClick={() => onClick(data.id)}
   >
    {/* 状态角标 */}
    {data.status && (
      <div className={styles.couponStatus}>
        {COUPON_STATUS[data.status]}
      </div>
    )}

    {/* 主要内容区域 */}
    <div className={styles.couponContent}>
      {/* 价格容器 */}
      <div className={styles.priceContainer}>
        <div className={styles.priceText}>
          {data.price?.priceText === '-' ? '免费' : data.price?.priceText}
        </div>
        {data.price?.priceUnit && data.price?.priceText !== '-' && (
          <div className={styles.priceUnit}>{data.price?.priceUnit}</div>
        )}
      </div>

      {/* 标题容器 */}
      <div className={styles.titleContainer}>
        <div className={styles.tag}>{data.label}</div>
        <div className={styles.title}>{data.title}</div>
        <div className={styles.dateRange}>{data.dateRange}</div>
      </div>
    </div>

    {/* 分割线 */}
    <div className={styles.divide}></div>

    {/* 使用规则 */}
    <div className={styles.rule}>
      <span>{data.rule}</span>
      
    </div>
   </div>
  );
};

export default CouponCard;
