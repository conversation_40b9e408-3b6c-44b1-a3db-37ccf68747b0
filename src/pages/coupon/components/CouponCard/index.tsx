import React from 'react';
import styles from './index.module.css';
import { RightOutline } from 'antd-mobile-icons';

// 优惠券样式配置常量
export const COUPON_STYLE_CONFIG = {
  // 平台优惠样式配置
  PLATFORM_SUBSIDY: {
    background: 'linear-gradient(135deg, #fff5f0 0%, #fff2e8 100%)',
    priceColor: '#ff6b35',
    tagBackground: 'rgba(255, 107, 53, 0.1)',
    tagColor: '#ff6b35',
    statusBackground: '#ff6b35',
  },
  // 活动优惠样式配置（包括行业优惠和活动优惠）
  PROMOTION_SUBSIDY: {
    background: 'linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%)',
    priceColor: '#0ea5e9',
    tagBackground: 'rgba(14, 165, 233, 0.1)',
    tagColor: '#0ea5e9',
    statusBackground: '#0ea5e9',
  },
  // 行业优惠使用与活动优惠相同的样式
  BIZ_SUBSIDY: {
    background: 'linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%)',
    priceColor: '#0ea5e9',
    tagBackground: 'rgba(14, 165, 233, 0.1)',
    tagColor: '#0ea5e9',
    statusBackground: '#0ea5e9',
  },
};

export interface CouponCardProps {
  id: string;
  label: string;
  price: {
    priceText: string;
    priceUnit: string | null;
  };
  title: string;
  dateRange: string;
  limit: string;
  rule: string;
  status: string;
  subsidyType: string;
  hidden: boolean;
}
const CouponCardBg = {
  // 待生效
  COUPON_AVAILABLE: {
  },
  // 生效中
  COUPON_USING: {
  },
  // 已使用
  COUPON_USED: {
    opacity: 0.7,
  },
  // 已失效
  COUPON_EXPIRE: {
    opacity: 0.7,
    filter: 'grayscale(100%)',
  },
  // 已失效
  COUPON_INVALID: {
    opacity: 0.7,
    filter: 'grayscale(100%)',
  },
};
const COUPON_STATUS = {
  COUPON_AVAILABLE: '待生效',
  COUPON_USING: '生效中',
  COUPON_USED: '已使用',
  COUPON_INVALID: '已失效',
  COUPON_EXPIRE: '已过期',
};

const CouponCard = ({data,onClick}:{data:CouponCardProps;onClick: (id: string) => void;}) => {
  // 获取当前优惠券的样式配置
  const styleConfig = COUPON_STYLE_CONFIG[data.subsidyType] || COUPON_STYLE_CONFIG.PLATFORM_SUBSIDY;

  return (
   <div
     className={styles.couponCard}
     style={{
       background: styleConfig.background,
       opacity: CouponCardBg[data.status]?.opacity ?? 1,
       filter: CouponCardBg[data.status]?.filter ?? 'none',
     }}
     onClick={() => onClick(data.id)}
   >
    {/* 状态角标 */}
    {data.status && (
      <div
        className={styles.couponStatus}
        style={{ background: styleConfig.statusBackground }}
      >
        {COUPON_STATUS[data.status]}
      </div>
    )}

    {/* 主要内容区域 */}
    <div className={styles.couponContent}>
      {/* 价格容器 */}
      <div className={styles.priceContainer}>
        <div
          className={styles.priceText}
          style={{ color: styleConfig.priceColor }}
        >
          {data.price?.priceText === '-' ? '免费' : data.price?.priceText}
        </div>
        {data.price?.priceUnit && data.price?.priceText !== '-' && (
          <div
            className={styles.priceUnit}
            style={{ color: styleConfig.priceColor }}
          >
            {data.price?.priceUnit}
          </div>
        )}
      </div>

      {/* 标题容器 */}
      <div className={styles.titleContainer}>
        <div className={styles.topArea}>
        <div
          className={styles.tag}
          style={{
            background: styleConfig.tagBackground,
            color: styleConfig.tagColor
          }}
        >
          {data.label}
        </div>
        <div className={styles.title}>{data.title}</div>
        </div>
        <div className={styles.dateRange}>{data.dateRange}</div>
      </div>
    </div>

    {/* 分割线 */}
    <div className={styles.divide}></div>

    {/* 使用规则 */}
    <div className={styles.rule}>
      <span>{data.rule}</span>
      
    </div>
   </div>
  );
};

export default CouponCard;
