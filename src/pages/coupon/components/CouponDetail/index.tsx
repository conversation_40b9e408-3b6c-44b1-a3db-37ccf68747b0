import { definePageConfig } from 'ice';
import { useEffect, useState } from 'react';
import styles from './index.module.css';
import { queryCouponById } from '@/api/query';
import { log } from '@ali/iec-dtao-utils';
import { money_US } from '@ali/iec-dtao-utils/esm/common/number';
import dayjs from 'dayjs';
import { useLoading } from '@/components/Loading';
import { getUrlParams } from '@/lib/params';

interface CouponDetailProps {
  couponTitle?: string;
  couponSubTitle?: string;
  conditionDescription?: string;
  rule?: string[];
  totalPromotionQuota: any;
  maxUseTimes: string | number;
}
const renderLimitText = (item) => {
  const text: string[] = [];
  if (item?.totalPromotionQuota?.value) {
    text.push(`【限制使用额度${money_US(item?.totalPromotionQuota?.value) || '-'}元】`);
  }
  if (item?.maxUseTimes && Number(item.maxUseTimes) > 0) {
    text.push(`【限制核销${item.maxUseTimes}次以内】`);
  }
  if (text?.length) {
    return text.join('');
  }
  return null;
};
const renderPrice = (item) => {
  const discountValue = item?.discount?.value;
  const fixPromotionValue = item?.fixPromotion?.value;
  try {
    if (item?.type === 'DISCOUNT_CARD' && discountValue) {
      if (Number(discountValue) > 0) {
        return {
          priceText: discountValue,
          priceUnit: '折',
        };
      }
      if (Number(discountValue) === 0) {
        return {
          priceText: '免费',
          priceUnit: null,
        };
      }
    } else if (item?.type === 'FIXED_PROMOITON_COUPON' && fixPromotionValue) {
      if (Number(fixPromotionValue) > 0) {
        return {
          priceText: `${money_US(fixPromotionValue) || '-'}`,
          priceUnit: '/单',
        };
      }
      if (Number(fixPromotionValue) === 0) {
        return {
          priceText: '免费',
          priceUnit: null,
        };
      }
    }
    return {
      priceText: '-',
      priceUnit: null,
    };
  } catch (error) {
    log.addLog('renderPrice-error', 'success', { message: JSON.stringify(error?.message) });
    return {
      priceText: '-',
      priceUnit: null,
    };
  }
};
const CouponDetail = () => {
  const [couponData, setCouponData] = useState<CouponDetailProps|null>(null);
  const { showLoading, hideLoading } = useLoading();
  const encodedId = getUrlParams(window.location.href, 'id');
  const couponId = encodedId ? decodeURIComponent(encodedId) : '';
  const handleOnQueryCouponId = async (id) => {
    showLoading('数据加载中...');
    try {
      const couponDetailData = await queryCouponById({
        couponId: id,
      });
      const { responseCode, merchantCouponDetail } = couponDetailData;
      if (responseCode !== 'SUCCESS') {
        log.addLog('query-coupon-detail', 'error', { responseCode });
        setCouponData(null);
        return;
      }
      const price = renderPrice(merchantCouponDetail);
      const dateRange = (merchantCouponDetail?.effectTime && merchantCouponDetail?.expireTime) ?
        `优惠有效期：${dayjs(merchantCouponDetail.effectTime).format('YYYY.MM.DD HH:mm')}～${dayjs(merchantCouponDetail.expireTime).format('YYYY.MM.DD HH:mm')}`
        : '优惠有效期：--';
      const descriptionList = merchantCouponDetail?.description?.split('\n');

      setCouponData({
        couponTitle: merchantCouponDetail?.title,
        couponSubTitle: `${price?.priceText}${price?.priceUnit ?? ''}券`,
        conditionDescription: merchantCouponDetail?.conditionDescription,
        rule: [dateRange, ...descriptionList],
        totalPromotionQuota: merchantCouponDetail?.totalPromotionQuota,
        maxUseTimes: merchantCouponDetail?.maxUseTimes,
      });
      hideLoading();
    } catch (error) {
      log.addLog('query-coupon-detail', 'error', { catch: error?.message });
      setCouponData(null);
    }
  };
  useEffect(() => {
    if (couponId) {
      handleOnQueryCouponId(couponId);
    }
  }, [couponId]);
  return (
    <div className={styles.couponDetail}>
      <div className={styles.titleArea}>
        <div className={styles.couponTitle}>{couponData?.couponTitle}</div>
        <div className={styles.couponDesc}>            {couponData?.couponSubTitle ? `${couponData?.couponSubTitle}，` : ''}{couponData?.conditionDescription || '-'}
          {renderLimitText(couponData) ?? ''}
        </div>
      </div>
      <div className={styles.ruleArea}>
        <div className={styles.ruleTitle}>使用规则</div>
        {couponData?.rule && couponData?.rule.map((item, index) => {
          return (
            <div key={index} className={styles.ruleDesc}>
              {index + 1}. {item}
            </div>
          );
        })}
      </div>
      <div className={styles.otherArea}>
        <div className={styles.otherTitle}>其他注意事项</div>
        <div className={styles.otherDesc}>若在获取或者使用过程中，如存在违规行为（如作弊领取、恶意套现、刷取信誉、虚假交易等），上海淘天商业管理有限公司有权取消您的优惠券使用资格，若已使用上海淘天商业管理有限公司有权追回；如给上海淘天商业管理有限公司造成损失或不良影响的，上海淘天商业管理有限公司保留追究赔偿的权利。活动期间如出现不可抗力或情势变更的情况，上海淘天商业管理有限公司无需为此承担赔偿责任或进行补偿。</div>
      </div>
    </div>
  );
};

export default CouponDetail;
export const pageConfig = definePageConfig(() => ({
  title: '优惠券详情',
  spm: {
    spmB: 'BYF_MERCHANT_UNIAPP-couponDetail',
  },
}));
