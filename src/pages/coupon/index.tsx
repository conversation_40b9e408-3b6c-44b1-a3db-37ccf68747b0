import { definePageConfig } from 'ice';
import Tabs from '../../components/Tabs';
import styles from './index.module.css';

const TAB_LIST = [{
  key: 'USING',
  title: '生效中',
}, {
  key: 'AVAILABLE',
  title: '待生效',
}, {
  key: 'UNAVAILABLE',
  title: '已失效',
}];

const Coupon = () => {
    const onChange=(type)=>{

    }
  const renderTabContent = (activeKey: string) => {
    switch (activeKey) {
      case 'USING':
        return <div className={styles.tabContent}>生效中的优惠券内容</div>;
      case 'AVAILABLE':
        return <div className={styles.tabContent}>待生效的优惠券内容</div>;
      case 'UNAVAILABLE':
        return <div className={styles.tabContent}>已失效的优惠券内容</div>;
      default:
        return null;
    }
  };

  return (
    <div className={styles.coupon}>
        <div className={styles.couponTabs}>
            <Tabs
                tabs={TAB_LIST}
                defaultActiveKey="USING"
                onChange={onChange}
            />
        </div>
        <div className={styles.couponList}>
            <h1>1121122</h1>
        </div>
    </div>
  );
}

export default Coupon;
export const pageConfig = definePageConfig(() => ({
  title: '退货宝',
  spm: {
    spmB: 'BYF_MERCHANT_UNIAPP-coupon',
  },
}));
