import { definePageConfig } from 'ice';
import Tabs from '../../components/Tabs';
import styles from './index.module.css';
import NoticeBar from '../../components/NoticeBar';
import { useState } from 'react';
const TAB_LIST = [{
  key: 'USING',
  title: '生效中',
}, {
  key: 'AVAILABLE',
  title: '待生效',
}, {
  key: 'UNAVAILABLE',
  title: '已失效',
}];
const TIP_CONTENT = {
  USING: '服务出单扣费时，系统将默认为您在生效且符合使用条件的权益中，选择最优的单项或叠加权益进行核销',
  UNAVAILABLE: '针对在权益有效期内创建并符合对应使用条件、且在权益失效后30天内发货的订单，出单扣费时仍可享受对应优惠',
};
const Coupon = () => {
    const [tipData,setTipData]=useState<Array<{type:string;content:string;}>|[]>([]);
    const onChange=(type)=>{
        console.log(type);
    }
  const renderTabContent = (activeKey: string) => {
    switch (activeKey) {
      case 'USING':
        return <div className={styles.tabContent}>生效中的优惠券内容</div>;
      case 'AVAILABLE':
        return <div className={styles.tabContent}>待生效的优惠券内容</div>;
      case 'UNAVAILABLE':
        return <div className={styles.tabContent}>已失效的优惠券内容</div>;
      default:
        return null;
    }
  };

  return (
    <div className={styles.coupon}>
        <div className={styles.couponTabs}>
            <Tabs
                tabs={TAB_LIST}
                defaultActiveKey="USING"
                onChange={onChange}
            />
        </div>
      <div className={styles.notice}>
           <NoticeBar
          content="您店铺风险较高，本无法开通。为支持店铺经营，开放特殊开通通道，请确认费率后开通"
          colorType='info'
          iconType='default'
        />
      </div>
        <div className={styles.couponList}>
            <h1>1121122</h1>
        </div>
    </div>
  );
}

export default Coupon;
export const pageConfig = definePageConfig(() => ({
  title: '退货宝',
  spm: {
    spmB: 'BYF_MERCHANT_UNIAPP-coupon',
  },
}));
