import { definePageConfig } from 'ice';
import { Tabs } from 'antd-mobile';
import { useState } from 'react';
import styles from './index.module.css';

const TAB_LIST = [{
  key: 'USING',
  title: '生效中',
}, {
  key: 'AVAILABLE',
  title: '待生效',
}, {
  key: 'UNAVAILABLE',
  title: '已失效',
}];

const Coupon = () => {
  const [activeKey, setActiveKey] = useState('USING');

  const renderTabContent = (key: string) => {
    switch (key) {
      case 'USING':
        return <div className={styles.tabContent}>生效中的优惠券内容</div>;
      case 'AVAILABLE':
        return <div className={styles.tabContent}>待生效的优惠券内容</div>;
      case 'UNAVAILABLE':
        return <div className={styles.tabContent}>已失效的优惠券内容</div>;
      default:
        return null;
    }
  };

  return (
    <div className={styles.coupon}>
      <Tabs
        activeKey={activeKey}
        onChange={setActiveKey}
        className={styles.tabs}
      >
        {TAB_LIST.map(tab => (
          <Tabs.Tab title={tab.title} key={tab.key}>
            {renderTabContent(tab.key)}
          </Tabs.Tab>
        ))}
      </Tabs>
    </div>
  );
}

export default Coupon;
export const pageConfig = definePageConfig(() => ({
  title: '退货宝',
  spm: {
    spmB: 'BYF_MERCHANT_UNIAPP-coupon',
  },
}));
