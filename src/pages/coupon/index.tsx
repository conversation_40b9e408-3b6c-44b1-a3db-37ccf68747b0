import { definePageConfig } from 'ice';
import Tabs from '../../components/Tabs';
import styles from './index.module.css';
import NoticeBar from '../../components/NoticeBar';
import CouponCard from '../../components/CouponCard';
import CouponDetail from '../../components/CouponDetail';
import { useEffect, useState } from 'react';
import { log } from '@ali/iec-dtao-utils';
import { getUrlParams } from '../../lib/params';
import { queryCouponList, queryCouponById } from '@/api/query';
import { money_US } from '@ali/iec-dtao-utils/esm/common/number';
import { useLoading } from '../../components/Loading';
import dayjs from 'dayjs';

const TAB_LIST = [{
  key: 'USING',
  title: '生效中',
}, {
  key: 'AVAILABLE',
  title: '待生效',
}, {
  key: 'UNAVAILABLE',
  title: '已失效',
}];
const TIP_CONTENT = {
  USING: '服务出单扣费时，系统将默认为您在生效且符合使用条件的权益中，选择最优的单项或叠加权益进行核销',
  UNAVAILABLE: '针对在权益有效期内创建并符合对应使用条件、且在权益失效后30天内发货的订单，出单扣费时仍可享受对应优惠；当前为您展示最近50个过期的权益，若有疑问请咨询平台客服',
};
const COUPON_NAME = {
  PROMOTION_SUBSIDY: '活动优惠',
  BIZ_SUBSIDY: '行业优惠',
  PLATFORM_SUBSIDY: '平台优惠',
};

const Coupon = () => {
  const [tipData, setTipData] = useState<string|null>(null);
  const [couponList, setCouponList] = useState<any[]>([]);
  const [selectedCouponId, setSelectedCouponId] = useState<string|null>(null);
  const [couponDetailData, setCouponDetailData] = useState<any>(null);
  const [showDetail, setShowDetail] = useState<boolean>(false);
  const { showLoading, hideLoading } = useLoading();
  const onChange = (type: string) => {
    // 先清空清空数据，避免闪烁
    setTipData(null);
    setCouponList([]);
    // 根据tab切换查询不同状态的优惠券
    handleOnQueryCouponList({ useStatus: type });
  };

  const handleOnQueryCouponList = async ({ useStatus }) => {
    showLoading('数据加载中...');
    try {
      const couponListData = await queryCouponList({
        useStatus,
      });
      const { responseCode, merchantCouponDetailList } = couponListData;
      if (responseCode !== 'SUCCESS') {
        log.addLog('query-coupon-list', 'error', { responseCode });
        setCouponList([]);
        setTipData(null);
        return;
      }
      const data = merchantCouponDetailList?.map((item: any) => {
        const itemData = {
          id: item.id,
          label: COUPON_NAME[item.subsidyType],
          price: renderPrice(item),
          title: renderTitle(item.title),
          dateRange: item?.effectTime && item?.expireTime ? `${dayjs(item.effectTime).format('YYYY.MM.DD HH:mm')}～${dayjs(item.expireTime).format('YYYY.MM.DD HH:mm')}` : '-',
          limit: renderLimitText(item),
          rule: item.conditionDescription,
          status: item.customStatus,
          subsidyType: item?.subsidyType,
          hidden: item?.hidden,
        };
        return itemData;
      });
      setCouponList(data || []);
      // 只有当有数据时才显示提示
      const contentData = TIP_CONTENT[useStatus] && data?.length > 0 ? TIP_CONTENT[useStatus] : null;
      setTipData(contentData);
      hideLoading();
    } catch (error) {
      log.addLog('query-coupon-list', 'error', { catch: error?.message });
      setCouponList([]);
      setTipData(null);
    }
  };
  // 有额度限制的券，要显示金额
  // 有次数限制的券，要显示次数
  const renderLimitText = (item) => {
    const text: string[] = [];
    if (item?.totalPromotionQuota?.value) {
      text.push(`额度${money_US(item?.totalPromotionQuota?.value) || '-'}元`);
    }
    if (item?.maxUseTimes) {
      text.push(`限${item.maxUseTimes}次`);
    }
    if (text?.length) {
      return text.join(' ');
    }
    return null;
  };
  // 券有可能显示折扣、免费、具体减免金额，后端通过discount.value返回返回
  const renderPrice = (item) => {
    const discountValue = item?.discount?.value;
    const fixPromotionValue = item?.fixPromotion?.value;
    try {
      if (item?.type === 'DISCOUNT_CARD' && discountValue) {
        if (Number(discountValue) > 0) {
          return {
            priceText: discountValue,
            priceUnit: '折',
          };
        }
        if (Number(discountValue) === 0) {
          return {
            priceText: '免费',
            priceUnit: null,
          };
        }
      } else if (item?.type === 'FIXED_PROMOITON_COUPON' && fixPromotionValue) {
        if (Number(fixPromotionValue) > 0) {
          return {
            priceText: `${money_US(fixPromotionValue) || '-'}`,
            priceUnit: '/单',
          };
        }
        if (Number(fixPromotionValue) === 0) {
          return {
            priceText: '免费',
            priceUnit: null,
          };
        }
      }
      return {
        priceText: '-',
        priceUnit: null,
      };
    } catch (error) {
      log.addLog('renderPrice-error', 'success', { message: JSON.stringify(error?.message) });
      return {
        priceText: '-',
        priceUnit: null,
      };
    }
  };
  // 移动端限制10字
  const renderTitle = (title: any) => {
    if (title && title.length > 10) {
      return `${title.slice(0, 10)}...`;
    }
    return title;
  };
  useEffect(() => {
    const tracelog = getUrlParams(window.location.href, 'traceLog');
    log.addLog('byf-coupon', 'visit', { from: tracelog ?? null });
    handleOnQueryCouponList({ useStatus: 'USING' });
  }, []);

  // 获取优惠券详情的函数
  const fetchCouponDetail = async (id: string) => {
    showLoading('加载详情中...');
    try {
      const couponDetailData = await queryCouponById({
        couponId: id,
      });
      const { responseCode, merchantCouponDetail } = couponDetailData;
      if (responseCode !== 'SUCCESS') {
        log.addLog('query-coupon-detail', 'error', { responseCode });
        setCouponDetailData(null);
        hideLoading();
        return;
      }

      // 处理价格显示
      const price = renderPrice(merchantCouponDetail);
      const dateRange = (merchantCouponDetail?.effectTime && merchantCouponDetail?.expireTime) ?
        `优惠有效期：${dayjs(merchantCouponDetail.effectTime).format('YYYY.MM.DD HH:mm')}～${dayjs(merchantCouponDetail.expireTime).format('YYYY.MM.DD HH:mm')}`
        : '优惠有效期：--';
      const descriptionList = merchantCouponDetail?.description?.split('\n') || [];

      const detailData = {
        couponTitle: merchantCouponDetail?.title,
        couponSubTitle: `${price?.priceText}${price?.priceUnit ?? ''}券`,
        conditionDescription: merchantCouponDetail?.conditionDescription,
        rule: [dateRange, ...descriptionList],
        totalPromotionQuota: merchantCouponDetail?.totalPromotionQuota,
        maxUseTimes: merchantCouponDetail?.maxUseTimes,
      };

      setCouponDetailData(detailData);
      setSelectedCouponId(id);
      setShowDetail(true);
      hideLoading();
    } catch (error) {
      log.addLog('query-coupon-detail', 'error', { catch: error?.message });
      setCouponDetailData(null);
      hideLoading();
    }
  };

  const handleOnClick = (id: string) => {
    console.log('点击优惠券:', id);
    fetchCouponDetail(id);
  };

  // 关闭详情页
  const handleCloseDetail = () => {
    setShowDetail(false);
    setSelectedCouponId(null);
    setCouponDetailData(null);
  };
  const renderListPanel = (list: any[]) => {
    if (!list?.length) {
      return (
        <div className={styles.emptyState}>
          <div className={styles.emptyText}>暂无权益</div>
        </div>
      );
    }

    return (
      <div className={styles.couponCardList}>
        {list.map((item) => {
          if (item?.hidden) {
            return null;
          }
          return (
            <CouponCard
              key={item.id}
              data={item}
              onClick={handleOnClick}
            />
          );
        })}
      </div>
    );
  };

  const renderTabContent = () => {
    return renderListPanel(couponList);
  };

  return (
    <div className={styles.coupon}>
      <div className={styles.couponTabs}>
        <Tabs
          tabs={TAB_LIST}
          defaultActiveKey="USING"
          onChange={onChange}
        />
      </div>
      {tipData && (
        <div className={styles.notice}>
          <NoticeBar
            content={tipData}
            colorType="info"
            iconType="default"
          />
        </div>
      )}
      <div className={styles.couponList}>
        {renderTabContent()}
      </div>

      {/* 优惠券详情弹窗 */}
      <CouponDetail
        visible={showDetail}
        couponData={couponDetailData}
        onClose={handleCloseDetail}
      />
    </div>
  );
};

export default Coupon;
export const pageConfig = definePageConfig(() => ({
  title: '我的权益',
  spm: {
    spmB: 'BYF_MERCHANT_UNIAPP-coupon',
  },
}));
