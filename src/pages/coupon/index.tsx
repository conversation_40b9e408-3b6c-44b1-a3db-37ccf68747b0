import { definePageConfig } from 'ice';
import Tabs from '../../components/Tabs';
import styles from './index.module.css';
import NoticeBar from '../../components/NoticeBar';
import CouponCard from './components/CouponCard';
import { useEffect, useState } from 'react';
import { log } from '@ali/iec-dtao-utils';
import { getUrlParams } from '../../lib/params';
import { queryCouponList } from '@/api/query';
import { money_US } from '@ali/iec-dtao-utils/esm/common/number';
import dayjs from 'dayjs';
const TAB_LIST = [{
  key: 'USING',
  title: '生效中',
}, {
  key: 'AVAILABLE',
  title: '待生效',
}, {
  key: 'UNAVAILABLE',
  title: '已失效',
}];
const TIP_CONTENT = {
  USING: '服务出单扣费时，系统将默认为您在生效且符合使用条件的权益中，选择最优的单项或叠加权益进行核销',
  UNAVAILABLE: '针对在权益有效期内创建并符合对应使用条件、且在权益失效后30天内发货的订单，出单扣费时仍可享受对应优惠',
};
const COUPON_NAME = {
  PROMOTION_SUBSIDY: '活动优惠',
  BIZ_SUBSIDY: '行业优惠',
  PLATFORM_SUBSIDY: '平台优惠',
};

const Coupon = () => {
    const [tipData,setTipData]=useState<string|null>(null);
    const [couponList,setCouponList]=useState<any[]>([]);
    const [activeTab, setActiveTab] = useState('USING');

    const onChange=(type: string)=>{
        setTipData(TIP_CONTENT[type]);
        setActiveTab(type);
        // 根据tab切换查询不同状态的优惠券
        handleOnQueryCouponList({ useStatus: type });
    }
    
    const handleOnQueryCouponList=async ({useStatus}) => {
         try {
      const couponListData = await queryCouponList({
        useStatus,
      });
      const { responseCode, merchantCouponDetailList } = couponListData;
      if (responseCode !== 'SUCCESS') {
        log.addLog('query-coupon-list', 'error', { responseCode });
        setCouponList([]);
        setTipData(null);
        return;
      }
      const data = merchantCouponDetailList?.map((item: any) => {
        // 根据subsidyType确定优惠券类型
        const getCardType = () => {
          if (item.subsidyType === 'PLATFORM_SUBSIDY') return 'platform';
          // PROMOTION_SUBSIDY(活动优惠) 和 BIZ_SUBSIDY(行业优惠) 都归类为activity
          if (item.subsidyType === 'PROMOTION_SUBSIDY' || item.subsidyType === 'BIZ_SUBSIDY') return 'activity';
          return 'platform';
        };

        // 根据status确定状态
        const getCardStatus = () => {
          if (item.status === 'COUPON_USING') return 'using';
          if (item.status === 'COUPON_USED') return 'used';
          return 'available';
        };

        // 处理优惠信息 - 直接使用API返回的price字段
        const getDiscountInfo = () => {
          const priceText = item.price?.priceText;
          const priceUnit = item.price?.priceUnit;

          if (!priceText || priceText === '-') {
            return {
              type: 'free' as const,
              value: '免费',
              unit: undefined
            };
          }
          if (priceUnit === '折') {
            return {
              type: 'discount' as const,
              value: priceText,
              unit: '折'
            };
          }
          if (priceUnit === '元') {
            return {
              type: 'reduce' as const,
              value: priceText,
              unit: '元'
            };
          }
          return {
            type: 'free' as const,
            value: '免费',
            unit: undefined
          };
        };

        // 解析时间范围
        const parseTimeRange = () => {
          if (item.dateRange) {
            const [effectTime, expireTime] = item.dateRange.split('～');
            return {
              effectTime: effectTime?.trim() || '',
              expireTime: expireTime?.trim() || ''
            };
          }
          return {
            effectTime: '',
            expireTime: ''
          };
        };

        const timeRange = parseTimeRange();

        const itemData = {
          id: item.id,
          type: getCardType(),
          status: getCardStatus(),
          title: item.title || '优惠券',
          discount: getDiscountInfo(),
          description: item.rule || '满足条件即可使用',
          effectTime: timeRange.effectTime,
          expireTime: timeRange.expireTime,
          label: item.label, // 传递具体的优惠券类型标签
        };
        return itemData;
      });
      console.log(data);
      setCouponList(data || []);
      setTipData(TIP_CONTENT[useStatus]);
    } catch (error) {
      log.addLog('query-coupon-list', 'error', { catch: error?.message });
      setCouponList([]);
      setTipData(null);
    }
    };
      const renderLimitText = (item) => {
  const text: string[] = [];
  if (item?.totalPromotionQuota?.value) {
    text.push(`额度${money_US(item?.totalPromotionQuota?.value) || '-'}元`);
  }
  if (item?.maxUseTimes) {
    text.push(`限${item.maxUseTimes}次`);
  }
  if (text?.length) {
    return text.join(' ');
  }
  return null;
};
const renderPrice = (item) => {
  const discountValue = item?.discount?.value;
  const fixPromotionValue = item?.fixPromotion?.value;
  try {
    if (item?.type === 'DISCOUNT_CARD' && discountValue) {
      if (Number(discountValue) > 0) {
        return {
          priceText: discountValue,
          priceUnit: '折',
        };
      }
      if (Number(discountValue) === 0) {
        return {
          priceText: '免费',
          priceUnit: null,
        };
      }
    } else if (item?.type === 'FIXED_PROMOITON_COUPON' && fixPromotionValue) {
      if (Number(fixPromotionValue) > 0) {
        return {
          priceText: `${money_US(fixPromotionValue) || '-'}`,
          priceUnit: '元',
        };
      }
      if (Number(fixPromotionValue) === 0) {
        return {
          priceText: '免费',
          priceUnit: null,
        };
      }
    }
    return {
      priceText: '-',
      priceUnit: null,
    };
  } catch (error) {
    log.addLog('renderPrice-error', 'success', { message: JSON.stringify(error?.message) });
    return {
      priceText: '-',
      priceUnit: null,
    };
  }
};
  const renderTitle = (title) => {
  if (title && title.length > 10) {
    return `${title.slice(0, 10)}...`;
  }
  return title;
};
  useEffect(() => {
    const tracelog = getUrlParams(window.location.href, 'traceLog');
    log.addLog('byf-coupon', 'visit', { from: tracelog ?? null });
    handleOnQueryCouponList({ useStatus: 'USING' });
  }, []);

  const renderTabContent = () => {
    if (!couponList || couponList.length === 0) {
      return (
        <div className={styles.emptyState}>
          <div className={styles.emptyText}>暂无优惠券</div>
        </div>
      );
    }

    return (
      <div className={styles.couponCardList}>
        {couponList.map((coupon: any) => (
          <CouponCard
            key={coupon.id}
            type={coupon.type}
            status={coupon.status}
            title={coupon.title}
            discount={coupon.discount}
            description={coupon.description}
            effectTime={coupon.effectTime}
            expireTime={coupon.expireTime}
            label={coupon.label}
            onClick={() => {
              console.log('点击优惠券:', coupon);
            }}
          />
        ))}
      </div>
    );
  };

  return (
    <div className={styles.coupon}>
        <div className={styles.couponTabs}>
            <Tabs
                tabs={TAB_LIST}
                defaultActiveKey="USING"
                onChange={onChange}
            />
        </div>
      <div className={styles.notice}>
           <NoticeBar
          content={tipData}
          colorType='info'
          iconType='default'
        />
      </div>
        <div className={styles.couponList}>
            {renderTabContent()}
        </div>
    </div>
  );
}

export default Coupon;
export const pageConfig = definePageConfig(() => ({
  title: '退货宝',
  spm: {
    spmB: 'BYF_MERCHANT_UNIAPP-coupon',
  },
}));
