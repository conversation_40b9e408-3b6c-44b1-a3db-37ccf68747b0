:global {
  .adm-list {
    .adm-list-body {
      border-top: none !important;
    }

    .adm-list-item {
      padding-left: 0 !important;
    }

    .adm-list-body-inner {
      .adm-list-item:first-child {
        .adm-list-item-content {
          border-top: none;
        }
      }
    }

    .adm-list-item-content {
      padding-right: 0 !important;
    }

    .adm-list-item-content-main {
      padding-top: 40rpx !important;
      padding-bottom: 28rpx !important;
    }

    .adm-list-item-content-arrow {
      margin-left: 44rpx !important;
    }

    .adm-collapse-panel-content-inner {
      .adm-list-item-content-main {
        padding: 0 40rpx 28rpx 44rpx !important;
      }
    }
  }

  .adm-button {
    font-weight: 500;
    line-height: 40rpx !important;
    padding: 18rpx 29rpx !important;
  }

  .adm-popup-body-position-bottom {
    border-top-left-radius: 12rpx !important;
    border-top-right-radius: 12rpx !important;
  }

  .adm-toast-mask {
    .adm-toast-main-text {
      padding: 12rpx !important;
    }

    .adm-toast-main {
      font-size: 32rpx;
      max-width: none !important;
    }
  }

  .adm-checkbox-custom-icon {
    display: flex;
    justify-content: center;
  }

  .adm-page-indicator {
    --active-dot-size: 12rpx !important;
    --dot-border-radius: 12rpx !important;
    --dot-size: 12rpx !important;
    --dot-spacing: 12rpx !important;
  }

  .adm-notice-bar-wrap {
    line-height: unset !important;
  }
  .adm-notice-bar {
    font-size: 26rpx !important;
    padding: 16rpx 24rpx !important;
  }

  .adm-notice-bar.adm-notice-bar-alert {
    background: rgba(255, 128, 0, 0.08) !important;
    border: none !important;
    color: #ff8000 !important;
  }

  .adm-notice-bar.adm-notice-bar-error {
    background: rgba(255, 0, 0, 0.08);
    color: #f00 !important;
    border: none !important;
  }

  .adm-notice-bar .adm-notice-bar-left {
    display: flex !important;
  }

  .adm-safe-area-position-bottom {
    padding-bottom: var(--safe-area-inset-bottom) !important;
  }

  .adm-swiper {
    --height: 100% !important;
  }

  .adm-swiper-horizontal .adm-swiper-indicator {
    bottom: 18rpx !important;
  }
}
