import { useEffect, useRef, useState } from 'react';
import { includes, isEmpty, isArray, get } from 'lodash-es';
import { queryDelivery } from '@/api/promotion';
import { log } from '@ali/iec-dtao-utils';
import { BasePopInfo, CouponPopInfo, OpenStatus } from '@/types';
import { ACTIVITY_COUNT } from '@/lib/constant';
import { fatigueJudgement } from '@/lib/fatigue';

const useFatigue = ({
  from,
  status,
  merchantStatusList,
  positionCode,
  isCouponPopClickApplyBtn,
}: {
  from: string;
  status: OpenStatus | string;
  merchantStatusList: string[];
  positionCode: string;
  isCouponPopClickApplyBtn?: { current: boolean };
}) => {
  // 基础弹窗信息
  const [basePopInfo, setBasePopInfo] = useState<BasePopInfo>({
    visible: false,
    imageUrl: '',
    actionUrl: '',
  });
  // 营销弹框信息
  const [couponPopInfo, setCouponPopInfo] = useState<CouponPopInfo>({
    couponPopVisible: false,
    couponList: [],
    openScene: '',
    effectiveDays: '',
    canNotQuitDays: '',
  });
  const [traceInfo, setTraceInfo] = useState<string>('');
  // 是否第一次发送请求：防止用增接口多次调用，导致疲劳度次数不精准
  const isFirst = useRef<boolean>(true);

  useEffect(() => {
    // 开通页
    if (
      from === 'open' &&
      isFirst.current &&
      !isCouponPopClickApplyBtn?.current &&
      positionCode &&
      includes(merchantStatusList, status)
    ) {
      isFirst.current = false;
      queryDeliveryRequest();
    }
    // 管理页
    if (
      from === 'manage' &&
      isFirst.current &&
      positionCode &&
      includes(merchantStatusList, status)
    ) {
      isFirst.current = false;
      queryDeliveryRequest();
    }
  }, [merchantStatusList, positionCode, status, isCouponPopClickApplyBtn?.current]);

  const queryDeliveryRequest = async () => {
    try {
      const marketRes = await queryDelivery({ positionCode });
      const { content: { items } } = marketRes as any;
      if (isEmpty(items) || !isArray(items)) {
        log.addLog('query-delivery-fail', 'error', { error: marketRes, from, positionCode });
        return;
      }
      // 限制活动数量
      const activityList = items.slice(0, ACTIVITY_COUNT);

      // 找出第一项没有疲劳度的活动
      const activityInfo = activityList.find((fatigueInfo) => {
        const { itemId, bizType, actionUrl, imageUrl, bizData, fatigue } = fatigueInfo || {};
        // 校验字段完整性
        if (bizType === 'ADVERTISING' && (isEmpty(actionUrl) || isEmpty(imageUrl) || isEmpty(fatigue))) {
          log.addLog('query-delivery-field-check-failed', 'error', { error: marketRes, from, positionCode, bizType });
          return false;
        }
        if (bizType === 'BYF_NEW_SELLER_OPEN' && (isEmpty(bizData) || isEmpty(fatigue))) {
          log.addLog('query-delivery-field-check-failed', 'error', { error: marketRes, from, positionCode, bizType });
          return false;
        }
        // 疲劳低控制
        return fatigueJudgement({ itemId, bizType, fatigue }, positionCode);
      });
      const ugTrack = get(activityInfo, 'ugTrack');
      setTraceInfo(ugTrack);
      log.addLog('query-delivery-success', 'success', { ugTrack, from, activityInfo, positionCode });
      // 没有活动
      if (isEmpty(activityInfo)) {
        log.addLog('query-delivery-no-activity-info', 'error', { ugTrack, error: marketRes, from, positionCode });
        return;
      }
      // 有活动
      const { imageUrl, actionUrl, bizType, bizData } = activityInfo || {};
      const { openScene, merchantTrialDetailList: couponList, attributes } = bizData || {};
      const { effectiveDays, canNotQuitDays } = attributes || {};
      log.addLog('query-delivery-activity-info', 'success', { ugTrack, from, activityInfo, positionCode, bizType });
      switch (bizType) {
        case 'ADVERTISING':
          log.addLog('query-delivery-base-popup-open', 'success', { ugTrack, from, positionCode, bizType });
          setBasePopInfo({
            visible: true,
            imageUrl,
            actionUrl,
          });
          break;
        case 'BYF_NEW_SELLER_OPEN':
          log.addLog('query-delivery-coupon-popup-open', 'success', { ugTrack, from, positionCode, bizType });
          setCouponPopInfo({
            openScene,
            effectiveDays,
            canNotQuitDays,
            couponPopVisible: true,
            couponList: formatCouponList(couponList),
          });
          break;
        default:
          break;
      }
    } catch (error) {
      log.addLog('query-delivery-fail', 'error', { error: error?.message, from, positionCode });
    }
  };

  const formatCouponList = (data) => {
    if (!Array.isArray(data)) {
      return data;
    }
    const couponInfo: any = [];
    data.forEach((item) => {
      const { originalFeeAmount, feeAmount, attributes } = item || {};
      const { title } = attributes || {};
      couponInfo.push({
        title,
        discountFeeAmount: feeAmount?.value,
        originalFeeAmount: originalFeeAmount?.value,
      });
    });
    return couponInfo;
  };

  const setBasePopInfoFun = (data) => setBasePopInfo({ ...basePopInfo, ...data });
  const setCouponPopInfoFun = (data) => setCouponPopInfo({ ...couponPopInfo, ...data });

  return {
    traceInfo,
    basePopInfo,
    couponPopInfo,
    setBasePopInfoFun,
    setCouponPopInfoFun,
  };
};

export default useFatigue;
