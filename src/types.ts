// 开通状态枚举
export type OpenStatus =
  | 'null'
  | 'MERCHANT_OPENING'
  | 'MERCHANT_OPEN_SUCCEED'
  | 'MERCHANT_OPEN_FAILED'
  | 'MERCHANT_FROZEN'
  | 'MERCHANT_QUITTING'
  | 'MERCHANT_QUIT_SUCCEED'
  | '';

export type AlipayChannel = 'TRADE_ACCOUNT' | 'FIN_ACCOUNT';
export type FixedPrice = 'YEAR' | 'HALF_YEAR' | 'SEASON'| 'MONTH';

interface BaseResponse {
  success?: boolean;
  responseCode?: string;
  responseMessage?: string;
}
export interface MerchantServiceStatusResponse extends BaseResponse {
  status: OpenStatus;
  fixedPriceType?: string;
  openFixedPrice?: boolean;
  failedCode?: string;
  customerFreezeCode?: string | null;
  giveType: string | null;
  openChannel?: string | null;
  currFixedPriceExpireSoon?: boolean | null;
  currFixedPriceRenewal?: boolean | null;
  currFixedPriceExpireTime?: number | string | null;
}

export interface BaseAdmitResponse extends BaseResponse {
  isAdmit: boolean;
  hasHighRiskRefund: boolean;
  openFixedPrice?: null | boolean;
  originalFeeAmount: string;
  fixedPriceType: FixedPrice;
  discountFeeAmount?: string;
  promotionFlag?: boolean;
  customerRejectCode?: string | null;
}

export interface AlipayInfoRequest {
  channel: AlipayChannel;
}

export interface ServiceOrderInfoRequest {
  mainOrderId: string | null;
}

export interface ClaimOrderListRequest {
  mainOrderId: string | null;
}

export interface ServiceOrderItem {
  itemId?: string;
  itemName?: string;
  itemNum?: string;
  itemUrl?: string;
  itemPicUrl?: string;
  skuId?: string;
  skuName?: string;
}

export interface ServiceInfo {
  serviceNo?: string | null;
  effectiveTime?: number | null;
  expireTime?: number | null;
  actualFeeAmount?: string | null;
  originalFeeAmount?: string | null;
  selfPostageGuaranteedAmount?: string | null;
}

export interface OrderInfoProps {
  mainOrderId: string | null;
  subOrderId: string | null;
  orderPayTime: number | null;
}

export interface ServiceOrderInfoResponse extends BaseResponse {
  serviceStatus: string;
  orderInfo: OrderInfoProps;
  serviceInfo: ServiceInfo;
  serviceOrderItemList: ServiceOrderItem[];
}

export interface ClaimDetailInfoListItem {
  compensationStatus?: string;
  compensatedAmount?: string | null;
  payee?: string | null;
  applyTime: number | null;
  compensatedTime: number | null;
}

export interface ClaimOrderListResponse extends BaseResponse {
  claimDetailInfoList: ClaimDetailInfoListItem[];
}

export interface AlipayInfoResponse extends BaseResponse {
  alipayLoginId: string;
}

export interface BaseOpenProps {
  identifier: string;
  fixedPriceType?: string;
  showFeeAmount: string;
  openChannel: string;
  showDiscountFeeAmount?: string;
  isChecked?: boolean;
  umidToken?: string;
  fixedPriceEffectTime?: number;
  fixedPriceExpireTime?: number;
}

export interface BaseOpenResponse extends BaseResponse {
  applicationNo?: string;
  originalFeeAmount?: string;
  discountFeeAmount?: string;
  promotionFlag?: boolean;
  feeAmount?: string;
}

export interface BaseTrailResponse extends BaseResponse {
  originalFeeAmount?: string;
  discountFeeAmount?: string;
  promotionFlag?: boolean;
}

export interface FixedAdmitResponse extends BaseResponse {
  originalFeeAmount?: string;
  fixedPriceType?: string | null;
  effectTime?: string | null;
  expireTime?: string | null;
}

export interface FixedOpenRequest {
  identifier: string;
  effectTime?: number | undefined;
  expireTime?: number | undefined;
  fixedPriceType?: string | undefined;
  showFeeAmount?: string | undefined;
}

export type FixedOpenResponse = BaseResponse;

export interface FixedTrailRequest {
  fixedPriceType?: string;
  openChannel: string;
}
// 单个固定价信息
export interface FixedAdmitAndTrailItem {
  originalFeeAmount?: string;
  fixedPriceType?: FixedPrice;
  effectTime: number | null;
  expireTime: number | null;
  discountFeeAmount?: string;
  promotionFlag?: boolean;
  isAdmit?: boolean;
}
export interface batchAdmitFixedResponse extends BaseResponse {
  fixedAdmitAndTrailInfoList: FixedAdmitAndTrailItem[];
}
export interface renewalBatchAdmitFixedResponse extends BaseResponse {
  renewalAdmitInfoList: FixedAdmitAndTrailItem[];
}
export interface FixedTrailResponse extends BaseResponse {
  originalFeeAmount: string;
  effectTime?: number | null;
  expireTime?: number | null;
  discountFeeAmount?: string;
  promotionFlag?: boolean;

}

export interface QueryMerchantServiceResponse extends BaseResponse {
  totalCompensationAmount?: string;
  totalServiceAmount?: string;
  totalGuaranteeDays?: number | null;
  totalBuyersOfNotGive?: string | null;
}

export interface QueryMerchantArrearsResponse extends BaseResponse {
  arrearsCalDate?: number | null;
  arrearsAmountMoreThan6Hours?: string | null;
  showArrears: boolean;
  arrears: boolean;
}

export interface BaseQuitRequest {
  identifier: string;
  scene: string;
  reason: string;
}

export interface BaseQuitResponse extends BaseResponse {
  applicationNo?: string;
}


export interface QueryDeliveryRequest {
  positionCode: string;
}

interface deliveryItems{
  fixedContent?: string;
}

export interface QueryDeliveryResponse {
  content: {
    items: deliveryItems[];
  };
}

export interface GiveAdmitRequest {
  openChannel: string;
}

export interface GiveAdmitResponse extends BaseResponse {
  status: string;
}

export interface PartSettingRequest {
  channel: string;
  identifier: string;
  giveType: string;
}

export type PartSettingResponse = BaseResponse;

export interface DataIndicatorListResponse extends BaseResponse {
  indicatorProcessData: any[];
  endTime: number;
}

// 价格信息基础类型
export interface PriceInfo {
  type: 'base' | 'fixed'; // 价格类型，日常价或固定价
  isAccess: boolean; // 是否准入
  feeAmount: string; // 价格金额
  fixedPriceType?: string; // 固定价类型，如 'YEAR' 或 'MONTH'（仅fixed类型使用）
  discountFeeAmount?: string; // 折扣后价格
  promotionFlag?: boolean; // 是否有划线价
  marketTitle?: string | null; // 营销标题
  effectTime?: number | null; // 生效时间（仅fixed类型使用）
  expireTime?: number | null; // 过期时间（仅fixed类型使用）
  customerRejectCode?: string | null; // 客户拒绝码
  isHighRisk?: boolean; // 是否高风险（仅base类型使用）
}
// 渲染信息接口，包含日常价和固定价
export type RenderInfo = PriceInfo[];
/**
 * 样式配置接口 - 定义价格卡片的视觉样式
 */
export interface StyleConfig {
  themeColor: string; // 主题色
  gradientFrom: string; // 背景渐变起始色
  gradientTo: string; // 背景渐变结束色
  selectedBorderColor: string; // 选中状态边框色
  noticeBackground: string; // 下方特权区域背景色
  noticeSelectedBackground: string; // 选中特权区域背景色
  arrowBackground: string; // 箭头背景色
  buttonBackground: string; // 开通按钮背景色（FixedOpenCard专用）
  manageNoticeBackground?: string; // 管理页 特权背景色
}

/**
 * 价格类型配置接口 - 定义价格类型的所有配置信息
 */
export interface PriceTypeConfig {
  title: string; // 价格卡片标题
  privilegeText: string; // 特权描述文案
  days: number; // 有效天数
  detailText?: string; // 详情说明文本
  isFixed: boolean; // 是否为固定价
  styles: StyleConfig; // 样式配置
}

export interface NewMerchantsOpenRequest {
  identifier: string;
  openChannel: string;
  openScene: string;
}
export interface NewMerchantsOpenResponse extends BaseResponse {
  applicationNo: string;
}

export interface BasePopInfo {
  visible: boolean;
  imageUrl: string;
  actionUrl: string;
}

export interface CouponPopInfo {
  couponPopVisible: boolean;
  openScene: string;
  effectiveDays: string;
  canNotQuitDays: string;
  couponList: any[];
}

export interface CouponListRequest {
  useStatus: string;
}

export interface CouponListResponse extends BaseResponse {
  merchantCouponDetailList: any;
  currentPage: number;
  totalRecord: number;
}
export interface CouponByIdRequest {
  couponId: string;
}
export interface CouponByIdResponse extends BaseResponse {
  merchantCouponDetail: any;
}
