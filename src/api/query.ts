import { BASE_URL, PRODUCT_CODE } from '@/lib/constant';
import {
  AlipayInfoRequest,
  AlipayInfoResponse,
  ClaimOrderListRequest,
  ClaimOrderListResponse,
  DataIndicatorListResponse,
  MerchantServiceStatusResponse,
  QueryMerchantArrearsResponse,
  QueryMerchantServiceResponse,
  ServiceOrderInfoRequest,
  ServiceOrderInfoResponse,
  CouponListRequest,
  CouponListResponse,
} from '@/types';
import { get,post } from '@/lib/ajax';

// 查询用户服务状态
export const queryMerchantStatus = (): Promise<MerchantServiceStatusResponse> => {
  return get({
    url: `${BASE_URL}/curl/${PRODUCT_CODE}/byfMerchantQueryService_queryMerchantStatus`,
  });
};

// 查询支付宝信息
export const queryAlipayInfo = (data: AlipayInfoRequest): Promise<AlipayInfoResponse> => {
  return get({
    url: `${BASE_URL}/curl/${PRODUCT_CODE}/byfMerchantQueryService_queryAlipayInfo`,
    data,
  });
};

// 查询用户汇总数据（保障天数&理赔单数）
export const queryMerchantSummary = (): Promise<QueryMerchantServiceResponse> => {
  return get({
    url: `${BASE_URL}/curl/${PRODUCT_CODE}/byfMerchantQueryService_queryMerchantSummary`,
  });
};

// 查询欠款数据
export const queryMerchantArrears = (): Promise<QueryMerchantArrearsResponse> => {
  return get({
    url: `${BASE_URL}/curl/${PRODUCT_CODE}/byfMerchantQueryService_queryMerchantArrears`,
  });
};

// 查询服务单（含商品信息）
export const queryServiceOrderInfo = (
  data: ServiceOrderInfoRequest,
): Promise<ServiceOrderInfoResponse> => {
  return get({
    url: `${BASE_URL}/curl/${PRODUCT_CODE}/byfMerchantQueryService_queryServiceOrderInfo`,
    data,
  });
};

// 查询补贴单列表
export const queryClaimOrderList = (
  data: ClaimOrderListRequest,
): Promise<ClaimOrderListResponse> => {
  return get({
    url: `${BASE_URL}/curl/${PRODUCT_CODE}/byfMerchantQueryService_queryClaimOrderList`,
    data,
  });
};

// 管理指标查询
export const queryDataIndicatorList = (): Promise<DataIndicatorListResponse> => {
  return get({
    url: `${BASE_URL}/curl/${PRODUCT_CODE}/byfMerchantQueryService_queryDataIndicatorList`,
  });
};
// 查询券列表
export const queryCouponList = (data: CouponListRequest): Promise<CouponListResponse> => {
  return post({
    url: `${BASE_URL}/curl/${PRODUCT_CODE}/byfMerchantQueryService_queryCouponList`,
    data,
  });
};
