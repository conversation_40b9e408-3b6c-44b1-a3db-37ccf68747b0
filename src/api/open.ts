import { BASE_URL, MOBILE_OPEN_CHANNEL, PRODUCT_CODE } from '@/lib/constant';
import {
  BaseAdmitResponse,
  BaseOpenProps,
  BaseOpenResponse,
  BaseTrailResponse,
  batchAdmitFixedResponse,
  FixedOpenResponse,
  FixedOpenRequest,
  FixedTrailRequest,
  FixedTrailResponse,
  GiveAdmitRequest,
  GiveAdmitResponse,
  NewMerchantsOpenRequest,
  NewMerchantsOpenResponse,
  PartSettingRequest,
  PartSettingResponse,
  renewalBatchAdmitFixedResponse,
} from '@/types';
import { get, post } from '@/lib/ajax';

// 查询日常准入&试算
export const baseAdmitAndTrailInfo = (): Promise<BaseAdmitResponse> => {
  return get({
    url: `${BASE_URL}/curl/${PRODUCT_CODE}/byfMerchantOpenService_baseAdmitAndTrailInfo`,
    data: { openChannel: MOBILE_OPEN_CHANNEL },
  });
};
// 固定价开通
export const fixedOpen = (data: FixedOpenRequest): Promise<FixedOpenResponse> => {
  return post({
    url: `${BASE_URL}/curl/${PRODUCT_CODE}/byfMerchantOpenService_fixedOpen`,
    data,
  });
};
// 赠送准入
export const giveAdmit = (data: GiveAdmitRequest): Promise<GiveAdmitResponse> => {
  return get({
    url: `${BASE_URL}/curl/${PRODUCT_CODE}/byfMerchantOpenService_giveAdmit`,
    data,
  });
};

// 服务设置
export const changePartSetting = (data: PartSettingRequest): Promise<PartSettingResponse> => {
  return post({
    url: `${BASE_URL}/curl/${PRODUCT_CODE}/byfMerchantOpenService_changePartSetting`,
    data,
  });
};

// 查询日常试算
export const baseTrail = (): Promise<BaseTrailResponse> => {
  return get({
    url: `${BASE_URL}/curl/${PRODUCT_CODE}/byfMerchantOpenService_baseTrail`,
    data: { openChannel: MOBILE_OPEN_CHANNEL },
  });
};

// 基础开通
export const baseOpen = (data: BaseOpenProps): Promise<BaseOpenResponse> => {
  return post({
    url: `${BASE_URL}/curl/${PRODUCT_CODE}/byfMerchantOpenService_baseOpen`,
    data,
  });
};

// 固定价单独试算
export const trailFixedPrice = (data: FixedTrailRequest): Promise<FixedTrailResponse> => {
  return get({
    url: `${BASE_URL}/curl/${PRODUCT_CODE}/byfMerchantOpenService_trailFixedPrice`,
    data,
  });
};
// 批量固定价准入试算
export const batchAdmitFixedPrice = (): Promise<batchAdmitFixedResponse> => {
  return get({
    url: `${BASE_URL}/curl/${PRODUCT_CODE}/byfMerchantOpenService_batchAdmitFixedPrice`,
    data: { openChannel: MOBILE_OPEN_CHANNEL },
  });
};

// 续签单独试算
export const renewalTrailFixedPrice = (data: FixedTrailRequest): Promise<FixedTrailResponse> => {
  return get({
    url: `${BASE_URL}/curl/${PRODUCT_CODE}/byfMerchantOpenService_renewalTrailFixedPrice`,
    data,
  });
};

// 批量续签准入试算
export const renewalBatchAdmitFixedPrice = (): Promise<renewalBatchAdmitFixedResponse> => {
  return get({
    url: `${BASE_URL}/curl/${PRODUCT_CODE}/byfMerchantOpenService_renewalBatchAdmitFixedPrice`,
    data: { openChannel: MOBILE_OPEN_CHANNEL },
  });
};

// 框类+日常一起开
export const baseAndFixedOpen = (data: BaseOpenProps): Promise<BaseOpenResponse> => {
  return post({
    url: `${BASE_URL}/curl/${PRODUCT_CODE}/byfMerchantOpenService_baseAndFixedOpen`,
    data,
  });
};

// 新商权益拉新开通
export const newMerchantsOpen = (data: NewMerchantsOpenRequest): Promise<NewMerchantsOpenResponse> => {
  return get({
    url: `${BASE_URL}/curl/${PRODUCT_CODE}/byfMerchantOpenService_newMerchantsOpen`,
    data,
  });
};
