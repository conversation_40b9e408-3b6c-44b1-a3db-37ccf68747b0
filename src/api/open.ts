import { BASE_URL, MOBILE_OPEN_CHANNEL, PRODUCT_CODE } from '@/lib/constant';
import {
  BaseAdmitResponse,
  BaseOpenProps,
  BaseOpenResponse,
  BaseTrailResponse,
  FixedAdmitAndTrailResponse,
  FixedRenewalAdmitResponse,
  FixedTrailRequest,
  FixedTrailResponse,
  GiveAdmitRequest,
  GiveAdmitResponse,
  PartSettingRequest,
  PartSettingResponse,
} from '@/types';
import { get, post } from '@/lib/ajax';

// 查询日常准入&试算
export const baseAdmitAndTrailInfo = (): Promise<BaseAdmitResponse> => {
  return get({
    url: `${BASE_URL}/curl/${PRODUCT_CODE}/byfMerchantOpenService_baseAdmitAndTrailInfo`,
    data: { openChannel: MOBILE_OPEN_CHANNEL },
  });
};

// 查询固定价准入&试算
export const fixedAdmitAndTrailInfo = (): Promise<FixedAdmitAndTrailResponse> => {
  return get({
    url: `${BASE_URL}/curl/${PRODUCT_CODE}/byfMerchantOpenService_fixedAdmitAndTrailInfo`,
    data: { openChannel: MOBILE_OPEN_CHANNEL },
  });
};

// 赠送准入
export const giveAdmit = (data: GiveAdmitRequest): Promise<GiveAdmitResponse> => {
  return get({
    url: `${BASE_URL}/curl/${PRODUCT_CODE}/byfMerchantOpenService_giveAdmit`,
    data,
  });
};

// 服务设置
export const changePartSetting = (data: PartSettingRequest): Promise<PartSettingResponse> => {
  return post({
    url: `${BASE_URL}/curl/${PRODUCT_CODE}/byfMerchantOpenService_changePartSetting`,
    data,
  });
};

// 查询年框续约准入
export const fixedRenewalAdmit = (data): Promise<FixedRenewalAdmitResponse> => {
  return get({
    url: `${BASE_URL}/curl/${PRODUCT_CODE}/byfMerchantOpenService_fixedRenewalAdmit`,
    data,
  });
};

// 查询日常试算
export const baseTrail = (): Promise<BaseTrailResponse> => {
  return get({
    url: `${BASE_URL}/curl/${PRODUCT_CODE}/byfMerchantOpenService_baseTrail`,
    data: { openChannel: MOBILE_OPEN_CHANNEL },
  });
};

// 查询固定价试算
export const fixedTrail = (data: FixedTrailRequest): Promise<FixedTrailResponse> => {
  return get({
    url: `${BASE_URL}/curl/${PRODUCT_CODE}/byfMerchantOpenService_fixedTrail`,
    data,
  });
};

// 日常开通
export const baseOpen = (data: BaseOpenProps): Promise<BaseOpenResponse> => {
  return post({
    url: `${BASE_URL}/curl/${PRODUCT_CODE}/byfMerchantOpenService_baseOpenV2`,
    data,
  });
};

// 查询年框续约试算
export const fixedRenewalTrail = (data: FixedTrailRequest): Promise<FixedTrailResponse> => {
  return get({
    url: `${BASE_URL}/curl/${PRODUCT_CODE}/byfMerchantOpenService_fixedRenewalTrail`,
    data,
  });
};
