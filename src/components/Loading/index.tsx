import { ToastHandler } from 'antd-mobile/es/components/toast';
import { useRef } from 'react';
import { Toast, SpinLoading } from 'antd-mobile';
import styles from './index.module.css';

export const useLoading = () => {
  const handler = useRef<ToastHandler>();

  const showLoading = (content: string) => {
    if (handler.current) {
      handler.current.close();
    }

    handler.current = Toast.show({
      content: (
        <div className={styles.loadingContainer}>
          <SpinLoading style={{ '--size': '60rpx' }} color="white" className={styles.spin} />
          {content}
        </div>
      ),
      duration: 0,
      maskClickable: false,
    });
  };

  const hideLoading = () => {
    handler.current?.close();
  };

  return {
    showLoading,
    hideLoading,
  };
};
