import styles from './index.module.css';
import { money_US } from '@ali/iec-dtao-utils/esm/common/number';
import { getPriceDisplay, getPriceTypeConfig, generateDynamicStyles } from '@/lib/tools';
import { PriceInfo } from '@/types';

interface PriceCardProps {
  priceInfo: PriceInfo; // 价格信息对象
  isSelected?: boolean; // 是否选中
  onSelect: () => void; // 选中回调
}

const PriceCard = ({
  priceInfo,
  isSelected = false,
  onSelect,
}: PriceCardProps) => {
  // 获取价格类型配置（标题、文案、样式等）
  const typeConfig = getPriceTypeConfig(priceInfo.type, priceInfo.fixedPriceType);

  // 获取价格显示配置（当前价格、原价、是否显示原价）
  const priceDisplay = getPriceDisplay(
    priceInfo.feeAmount,
    priceInfo.discountFeeAmount,
    priceInfo.promotionFlag,
  );

  // 生成动态样式变量
  const dynamicStyles = generateDynamicStyles(typeConfig);

  // 类名组合函数
  const getClassName = (baseClass: string, includeSelected = true) => {
    const classes = [
      baseClass,
      typeConfig.isFixed ? styles.fixed : styles.base,
    ];
    if (includeSelected && isSelected) {
      classes.push(styles.selected);
    }
    return classes.join(' ');
  };

  return (
    <div className={styles.priceCard} style={dynamicStyles}>
      <div className={getClassName(styles.priceCardContent)} onClick={onSelect}>
        {/* 价格信息区域 */}
        <div className={styles.priceInfo}>
          <div className={getClassName(styles.priceTitle)}>
            {typeConfig.title}
          </div>
          <div className={styles.priceDetails}>
            {/* 当前价格 */}
            <div className={getClassName(styles.currentPrice)}>
              <div className={styles.priceValue}>
                {money_US(priceDisplay.currentPrice)}
              </div>
              <div className={styles.priceUnit}>元/单</div>
            </div>
            {/* 原价（划线价格） */}
            {priceDisplay.showOriginalPrice && (
              <div className={getClassName(styles.originalPrice)}>
                <span className={styles.originalPriceValue}>
                  {money_US(priceDisplay.originalPrice)}
                </span>
                <span className={styles.originalPriceUnit}>元/单</span>
              </div>
            )}
          </div>
        </div>

        {/* 特权通知区域 */}
        <div className={getClassName(styles.updateNotice)} style={{ position: 'relative' }}>
          <div className={getClassName(styles.updateNoticeText)}>
            {typeConfig.privilegeText}
          </div>
          {isSelected && (
            <div className={getClassName(styles.updateNoticeArrow, false)} />
          )}
        </div>
      </div>
    </div>
  );
};

export default PriceCard;
