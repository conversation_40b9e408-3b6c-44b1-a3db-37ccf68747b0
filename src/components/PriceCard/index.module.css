/* 基础样式 */
.priceCard {
  display: flex;
  width: 100%;
  height: 100%;
}

.priceCardContent {
  padding-top: 34rpx;
  width: 100%;
  display: flex;
  flex-direction: column;
  border: 3rpx solid transparent;
  background: #f7f8fa;
  border-radius: 16rpx 48rpx 16rpx 16rpx;
  position: relative;
  cursor: pointer;
}

/* 类型标识符 - 这些类用于组合选择器 */
.fixed {
  /* 固定价类型标识 */
}

.base {
  /* 日常价类型标识 */
}

.selected {
  /* 选中状态标识 */
}

/* 配置化选中状态样式 */
.priceCardContent.base.selected,
.priceCardContent.fixed.selected {
  border: 3rpx solid var(--selected-border-color);
  background: linear-gradient(180deg, var(--gradient-from) 0%, var(--gradient-to) 100%);
}

/* 固定价选中状态的标签 */
.priceCardContent.fixed.selected::before {
  content: "承诺有效期内不退出";
  position: absolute;
  top: -16rpx;
  left: -3rpx;
  background: var(--theme-color);
  color: #fff;
  font-size: 24rpx;
  font-weight: 400;
  border-radius: 8rpx 12rpx 8rpx 0rpx;
  padding: 4rpx 12rpx;
  height: 28rpx;
  z-index: 3;
  display: flex;
  align-items: center;
  justify-content: center;
  line-height: 1;
}

.priceInfo {
  display: flex;
  flex-direction: column;
  align-self: center;
  flex: 1;
  width: 100%;
}

.priceTitle {
  color: #111;
  font-size: 26rpx;
  font-weight: 500;
  line-height: 30rpx;
  align-self: center;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

.priceTitle.fixed.selected {
  color: var(--theme-color);
}

.priceDetails {
  display: flex;
  flex-direction: column;
  margin-top: 24rpx;
  align-self: center;
}

.currentPrice {
  display: flex;
  align-items: flex-end;
  gap: 2rpx;
}

.currentPrice.fixed {
  color: var(--theme-color);
}

.currentPrice.base.selected {
  color: var(--theme-color);
}

.priceValue {
  font-family: "AlibabaFontMd";
  font-size: 48rpx;
  font-weight: 500;
  line-height: 100%;
  letter-spacing: normal;
}

.priceUnit {
  font-size: 24rpx;
  margin-bottom: 7rpx;
  white-space: nowrap;
  margin: 0;
}

.originalPrice {
  color: #ccc;
  font-size: 24rpx;
  font-weight: 400;
  line-height: 28rpx;
  text-decoration: line-through;
  font-family: "AlibabaFontMd";
  align-self: center;
  margin: 4rpx 0 0 1rpx;
  display: flex;
  align-items: flex-end;
}

.originalPrice.fixed.selected {
  color: color-mix(in srgb, var(--theme-color) 38%, transparent);
}

.originalPriceValue,
.originalPriceUnit {
  font-size: 24rpx;
  font-weight: 400;
  line-height: 24rpx;
  text-decoration: line-through;
}

.updateNotice {
  border-radius: 16rpx;
  display: flex;
  flex-direction: row;
  margin-top: 30rpx;
  justify-content: center;
  align-self: center;
  width: 100%;
  height: 56rpx;
  padding: 16rpx 12rpx;
  box-sizing: border-box;
  background: var(--notice-background);
}

.updateNotice.fixed {
  background: var(--notice-background);
}

.updateNotice.fixed.selected {
  background: var(--notice-selected-background);
}

.updateNotice.base {
  background: #f0f2fa;
}

.updateNoticeText {
  font-size: 22rpx;
  line-height: 100%;
  align-self: flex-start;
  max-width: 662rpx;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

.updateNoticeText.fixed {
  color: var(--theme-color);
}

.updateNoticeText.fixed.selected {
  font-weight: 500;
  color: var(--theme-color);
}

.updateNoticeText.base.selected {
  font-weight: 500;
}

.updateNoticeArrow {
  position: absolute;
  bottom: -11rpx;
  left: 50%;
  transform: translateX(-50%) rotate(45deg);
  width: 16rpx;
  height: 16rpx;
  border-right: 3rpx solid;
  border-bottom: 3rpx solid;
  z-index: 2;
  background: var(--arrow-background);
  border-color: var(--theme-color);
}

.updateNoticeArrow.fixed {
  background: var(--arrow-background);
  border-color: var(--theme-color);
}

.updateNoticeArrow.base {
  background: #f0f2fa;
  border-color: var(--theme-color);
} 