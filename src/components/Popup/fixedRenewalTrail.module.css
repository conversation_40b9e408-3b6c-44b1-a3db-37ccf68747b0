.fixedRenewalTrail {
  padding: 24rpx;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  gap: 30rpx;
}

.title {
  font-size: 32rpx;
  font-weight: 500;
  line-height: 48rpx;
  color: var(--text-base);
}

.noticeBarIcon {
  height: 36rpx;
  width: 36rpx;
}

.subTitle {
  font-size: 26rpx;
  font-weight: 500;
  line-height: 40rpx;
  color: #111;
}

.priceContainer {
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.fixedRenewalPrice {
  font-size: 24rpx;
  font-weight: normal;
  line-height: 100%;
  color: #111;

  span {
    font-family: Alibaba Sans 102;
    font-size: 36rpx;
    font-weight: 500;
    line-height: 100%;
  }
}

.originalPrice {
  font-size: 24rpx;
  font-weight: normal;
  color: #999;
  line-height: 28rpx;
  text-decoration: line-through;
  text-decoration-color: rgba(153, 153, 153);
  align-self: flex-end;
  display: flex;
  align-items: center;
  
  span {
    font-family: Alibaba Sans 102;
    font-size: 24rpx;
    font-weight: normal;
  }
}

.questionIcon {
  width: 26rpx;
  height: 24rpx;
  padding: 0;
  cursor: pointer;
}

.time {
  font-size: 24rpx;
  font-weight: normal;
  line-height: 100%;
  color: #666;
}

.questionBalloon {
  position: relative;
  left: 112rpx;
  bottom: 407rpx;
}

:global(.adm-notice-bar) {
  border: none;
  border-radius: 12rpx;
}