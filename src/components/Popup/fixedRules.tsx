import { Popup } from 'antd-mobile';
import styles from './fixedRules.module.css';

interface FixedRulesProps {
  visible: boolean;
  fixedPriceType?: string; // 框的类型 标题
  onClose?: () => void;
}

const FixedRules = ({ visible, onClose, fixedPriceType }: FixedRulesProps) => {
  let fixedPriceTitle = '';
  switch (fixedPriceType) {
    case 'YEAR':
      fixedPriceTitle = '年框一口价';
      break;
    case 'HALF_YEAR':
      fixedPriceTitle = '半年框一口价';
      break;
    case 'SEASON':
      fixedPriceTitle = '季框一口价';
      break;
    case 'MONTH':
      fixedPriceTitle = '月框一口价';
      break;
    default:
      fixedPriceTitle = '一口价';
      break;
  }
  return (
    <Popup
      visible={visible}
      onMaskClick={onClose}
      onClose={onClose}
      showCloseButton
    >
      <div className={styles.area}>
        <div className={styles.title}>
          <div className={styles.titleText}>
            {fixedPriceTitle}
          </div>
        </div>
        <div className={styles.content}>
          <div className={styles.contentIntro}>该活动是限时邀请制，只针对部分符合准入条件的商家开放，商家不能主动申请参与。</div>
          <div className={styles.contentTitle}>活动规则如下</div>
          <div className={styles.contentDetail}>1. 商家需要约定一定周期的服务在约时长，即可享受专属一口价的优惠。在这个约定的时间内，服务费价格将保持不变。
          </div>
          <div className={styles.contentDetail}>2. 签约后，在这个约定的时间内，商家不能中途退出此服务。服务到期后将恢复日常动态定价。</div>
        </div>
      </div>
    </Popup>
  );
};

export default FixedRules;
