import { Popup, NoticeBar as AntdNoticeBar } from 'antd-mobile';
import { useCallback, useState } from 'react';
import styles from './fixedRenewalTrail.module.css';
import dayjs from 'dayjs';
import { ICON_MAP, ORIGINAL_PRICE_DESC } from '@/lib/constant';
import { number } from '@ali/iec-dtao-utils';
import Balloon from '../Balloon';
import { getPriceDisplay } from '@/lib/tools';

const { money_US } = number;
const QuestionIcon =
  'https://gw.alicdn.com/imgextra/i4/O1CN01GoG99Z1dkXkhCNZfV_!!6000000003774-55-tps-23-23.svg';

interface PopupOptions {
  fixedRenewalData: {
    promotionFlag?: boolean;
    discountFeeAmount?: string;
    originalFeeAmount?: string;
    effectTime?: number | null;
    expireTime?: number | null;
    fixedPriceType?: string;
  };
  visible: boolean;
  setVisible: (visible: boolean) => void;
  title: string;
  onClose?: () => void;
}

// 根据fixedPriceType获取对应的标题
const getFixedPriceTitle = (fixedPriceType?: string) => {
  switch (fixedPriceType) {
    case 'YEAR':
      return '年框一口价';
    case 'MONTH':
      return '月框一口价';
    default:
      return '年框一口价'; // 默认显示年框
  }
};

const FixedRenewalTrail = ({
  fixedRenewalData,
  visible,
  setVisible,
  title,
  onClose,
}: PopupOptions) => {
  const [questionPopupVisible, setQuestionPopupVisible] = useState(false);

  const handleClose = useCallback(() => {
    setVisible(false);
    onClose?.();
  }, [onClose, setVisible]);

  const handleQuestionClick = useCallback(() => {
    setQuestionPopupVisible(true);
  }, []);

  const fixedPriceTitle = getFixedPriceTitle(fixedRenewalData?.fixedPriceType);

  // 使用统一的价格显示配置
  const priceDisplay = getPriceDisplay(
    fixedRenewalData?.originalFeeAmount || '',
    fixedRenewalData?.discountFeeAmount,
    fixedRenewalData?.promotionFlag,
  );

  return (
    <>
      <Popup bodyStyle={{ height: '45vh' }} visible={visible} showCloseButton onClose={handleClose}>
        <div className={styles.fixedRenewalTrail}>
          <div className={styles.title}>{title}</div>
          <AntdNoticeBar
            className={styles.noticeBar}
            content={
              <div className={styles.noticeBarContent}>
                恭喜您提前锁定，续签的{fixedPriceTitle}将于{fixedRenewalData?.effectTime ? dayjs(fixedRenewalData?.effectTime).format('YYYY年MM月DD日') : '--'}生效
              </div>
            }
            color={'info'}
            icon={<img src={ICON_MAP.infoDefault} className={styles.noticeBarIcon} />}
            wrap
          />
          <div className={styles.subTitle}>{fixedPriceTitle}</div>
          <div className={styles.priceContainer}>
            <div className={styles.fixedRenewalPrice}>
              <span>¥{money_US(priceDisplay.currentPrice || '0')}</span>/单
            </div>
            {priceDisplay.showOriginalPrice && (
              <>
                <div className={styles.originalPrice}>
                  <span>¥{money_US(priceDisplay.originalPrice)}</span>/单
                  <img src={QuestionIcon} className={styles.questionIcon} onClick={handleQuestionClick} />
                </div>
              </>
            )}
          </div>
          <div className={styles.time}>
            有效期：{fixedRenewalData?.effectTime ? dayjs(fixedRenewalData?.effectTime).format('YYYY-MM-DD') : '--'} ~ {fixedRenewalData?.expireTime ? dayjs(fixedRenewalData?.expireTime).format('YYYY-MM-DD') : '--'}
          </div>

          {/* 使用通用气泡组件 */}
          <div className={styles.questionBalloon}>
            <Balloon
              visible={questionPopupVisible}
              setVisible={setQuestionPopupVisible}
              desc={ORIGINAL_PRICE_DESC}
            />
          </div>
        </div>
      </Popup>
    </>
  );
};

export default FixedRenewalTrail;
