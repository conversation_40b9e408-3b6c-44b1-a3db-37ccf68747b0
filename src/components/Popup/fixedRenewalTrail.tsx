import { Popup, NoticeBar as AntdNoticeBar } from 'antd-mobile';
import { useCallback } from 'react';
import styles from './fixedRenewalTrail.module.css';
import dayjs from 'dayjs';
import { ICON_MAP } from '@/lib/constant';
import { number } from '@ali/iec-dtao-utils';

const { money_US } = number;

interface PopupOptions {
  fixedRenewalData: {
    originalFeeAmount?: string;
    effectTime?: number | null;
    expireTime?: number | null;
  };
  visible: boolean;
  setVisible: (visible: boolean) => void;
  title: string;
  onClose?: () => void;
}

const FixedRenewalTrail = ({
  fixedRenewalData,
  visible,
  setVisible,
  title,
  onClose,
}: PopupOptions) => {
  const handleClose = useCallback(() => {
    setVisible(false);
    onClose?.();
  }, [onClose, setVisible]);

  return (
    <Popup bodyStyle={{ height: '45vh' }} visible={visible} showCloseButton onClose={handleClose}>
      <div className={styles.fixedRenewalTrail}>
        <div className={styles.title}>{title}</div>
        <AntdNoticeBar
          className={styles.noticeBar}
          content={
            <div className={styles.noticeBarContent}>
              恭喜您提前锁定，续签的年框一口价将于{fixedRenewalData?.effectTime ? dayjs(fixedRenewalData?.effectTime).format('YYYY年MM月DD日') : '--'}生效
            </div>
          }
          color={'info'}
          icon={<img src={ICON_MAP.infoDefault} className={styles.noticeBarIcon} />}
          wrap
        />
        <div className={styles.subTitle}>年框一口价</div>
        <div className={styles.fixedRenewalPrice}>
          <span>¥{money_US(fixedRenewalData?.originalFeeAmount)}</span>/单
        </div>
        <div className={styles.time}>
          有效期：{fixedRenewalData?.effectTime ? dayjs(fixedRenewalData?.effectTime).format('YYYY-MM-DD') : '--'} ~ {fixedRenewalData?.expireTime ? dayjs(fixedRenewalData?.expireTime).format('YYYY-MM-DD') : '--'}
        </div>
      </div>
    </Popup>
  );
};

export default FixedRenewalTrail;
