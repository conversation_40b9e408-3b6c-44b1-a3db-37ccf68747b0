import { Button, Popup, SafeArea } from 'antd-mobile';
import { useCallback } from 'react';
import { PROTOCOL_URL } from '@/lib/constant';
import styles from './agreements.module.css';

interface PopupOptions {
  visible: boolean;
  setVisible: (visible: boolean) => void;
}

const AgreementPopup = ({ visible, setVisible }: PopupOptions) => {
  const handleClose = useCallback(() => {
    setVisible(false);
  }, [setVisible]);

  return (
    <Popup visible={visible} bodyStyle={{ height: '80vh' }} showCloseButton onClose={handleClose}>
      <div className={styles.agreement}>
        <div className={styles.title}>协议详情</div>
        <iframe src={PROTOCOL_URL} />
        <Button color="primary" onClick={handleClose}>
          确认
        </Button>
        <SafeArea position="bottom" />
      </div>
    </Popup>
  );
};

export default AgreementPopup;
