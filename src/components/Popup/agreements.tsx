import { Button, <PERSON>up, SafeArea } from 'antd-mobile';
import { useCallback } from 'react';
import styles from './agreements.module.css';

const PROTOCOL_URL =
  'https://terms.alicdn.com/legal-agreement/terms/b_end_product_protocol/20240731161354834/20240731161354834.html';

interface PopupOptions {
  visible: boolean;
  setVisible: (visible: boolean) => void;
}

const AgreementPopup = ({ visible, setVisible }: PopupOptions) => {
  const handleClose = useCallback(() => {
    setVisible(false);
  }, [setVisible]);

  return (
    <Popup visible={visible} bodyStyle={{ height: '80vh' }} showCloseButton onClose={handleClose}>
      <div className={styles.agreement}>
        <div className={styles.title}>协议详情</div>
        <iframe src={PROTOCOL_URL} />
        <Button color="primary" onClick={handleClose}>
          确认
        </Button>
        <SafeArea position="bottom" />
      </div>
    </Popup>
  );
};

export default AgreementPopup;
