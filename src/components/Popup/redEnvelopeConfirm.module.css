.redEnvelopeContainer {
  font-family: PingFang SC;
  padding: 34rpx 37rpx 16px;
  display: flex;
  flex-direction: column;
  height: 100%;
  box-sizing: border-box;
  background-image: url('https://gw.alicdn.com/imgextra/i4/O1CN013eJJIH1E44g9tMzpR_!!6000000000297-2-tps-750-732.png');
  background-size: 100% 100%;
  background-repeat: no-repeat;
}

.redEnvelopeContainer > .title {
  font-family: DingTalk JinBuTi;
  font-size: 36rpx;
  font-weight: normal;
  line-height: 54rpx;
  background: linear-gradient(95deg, #f27 0%, #e43f63 42%, #ac2249 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-fill-color: transparent;
  margin-bottom: 12rpx;
}

.redEnvelopeContainer .subTitle {
  color: var(--text-secondary);
  font-size: 26rpx;
  line-height: 42rpx;
  color: #7a8599;
  margin-bottom: 18rpx;
}

.redEnvelopeContainer .container {
  display: flex;
  justify-content: space-between;
  height: 275rpx;
  margin-bottom: 32rpx;
  background: url('https://gw.alicdn.com/imgextra/i3/O1CN01vAfARu1bo5dNePVCo_!!6000000003511-2-tps-682-275.png');
  background-repeat: no-repeat;
  background-size: 100% 100%;
}

.redEnvelopeContainer .desc {
  color: #999;
  font-size: 22rpx;
  margin-bottom: 39rpx;
}

.redEnvelopeContainer .desc span {
  margin-right: 6rpx;
}

/* 当前服务费 & 领取权益后预计的服务费 公共样式 */
.container .serviceFee {
  display: flex;
  flex-direction: column;
  align-items: space-between;
  justify-content: center;
  height: 100%;
}

.container .title {
  font-size: 24rpx;
  font-weight: 600;
  color: #111;
  padding: 6rpx 36rpx;
  margin-bottom: 24rpx;
}
.container .originalAmount {
  color: #999;
  font-size: 24rpx;
  text-decoration: line-through;
}
.redEnvelopeContainer .actionArea {
  width: 100%;
  display: flex;
  justify-content: space-between;
  gap: 16rpx;
  padding-top: 14rpx;
  padding-bottom: 14rpx;

  button {
    flex: 1;
  }
}
.container .footerBtn {
  background: linear-gradient(257deg, #ff912b 0%, #ff5a5a 99%, #ff4544 99%);
}

/* 当前服务费 */
.currentServiceFee {
  font-family: Alibaba Sans 102;
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-left: 21rpx;
  color: #7c270f;
}
.currentServiceFee .title {
  background: linear-gradient(
    90deg,
    rgba(255, 177, 61, 0) 0%,
    rgba(255, 177, 61, 0.1) 23%,
    rgba(255, 177, 61, 0.1) 80%,
    rgba(255, 177, 61, 0) 100%
  );
}
.currentServiceFee .currentAmount {
  margin-bottom: 12rpx;
}
.currentServiceFee .feeAmount {
  font-size: 52rpx;
}
.currentServiceFee .symbol {
  font-weight: 500;
  font-size: 36rpx;
}
.currentServiceFee .unit {
  font-weight: 600;
  font-size: 24rpx;
  color: #7c270f;
  margin-left: 6rpx;
}
.currentServiceFee .integerAmount {
  font-weight: 500;
  font-size: 52rpx;
}
.currentServiceFee .decimalAmount {
  font-weight: 500;
  font-size: 36rpx;
  margin-right: 6rpx;
}
.currentServiceFee .originalAmount {
  color: #999;
  font-size: 24rpx;
  text-decoration: line-through;
}

/* 领取权益后预计的服务费 */
.container .discountServiceFee {
  font-family: Alibaba Sans 102;
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-right: 19rpx;
  margin-bottom: 24rpx;
}
.discountServiceFee .title {
  background: linear-gradient(
    90deg,
    rgba(255, 103, 61, 0) 0%,
    rgba(255, 103, 61, 0.2) 23%,
    rgba(255, 103, 61, 0.1) 80%,
    rgba(255, 103, 61, 0) 100%
  );
}
.discountServiceFee .discountAmount {
  margin-bottom: 12rpx;
  background: linear-gradient(109deg, #ff2f39 0%, #fc903b 109%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-fill-color: transparent;
}
.discountServiceFee .feeAmount {
  font-size: 68rpx;
}
.discountServiceFee .symbol {
  font-weight: 500;
  font-size: 42rpx;
}
.discountServiceFee .unit {
  font-weight: 600;
  font-size: 32rpx;
  margin-left: 6rpx;
}
.discountServiceFee .integerAmount {
  font-weight: 500;
  font-size: 52rpx;
  font-size: 68rpx;
}
.discountServiceFee .decimalAmount {
  font-weight: 500;
  margin-right: 6rpx;
  font-size: 48rpx;
}
