.popup {
  padding: 0 32rpx;

  display: flex;
  flex-direction: column;
  align-items: center;
  height: 100%;

  button {
    width: 100%;
    margin: 18rpx 0;
  }
}

.title {
  font-size: 32rpx;
  font-weight: 500;
  line-height: 48rpx;
  color: var(--text-base);
}

.icon {
  width: 60rpx;
  margin-top: 72rpx;
  margin-bottom: 24rpx;
}

.noIconTitle {
  margin-top: 72rpx;
}

.actionArea {
  width: 100%;
  display: flex;
  justify-content: space-between;
  gap: 16rpx;
  margin-top: 14rpx;
  margin-bottom: 14rpx;

  button {
    flex: 1;
  }
}

.desc {
  font-size: 26rpx;
  font-weight: 400;
  line-height: 40rpx;
  color: var(--text-secondary);
  flex-grow: 1;
  margin: 24rpx 0 72rpx 0;
}

.descMiddle {
  text-align: center;
  max-width: 450rpx;
}

.descFull {
  text-align: left;
}
