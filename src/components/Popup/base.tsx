import { <PERSON>up, Button, SafeArea } from 'antd-mobile';
import { useCallback } from 'react';
import styles from './base.module.css';

interface PopupOptions {
  visible: boolean;
  setVisible: (visible: boolean) => void;
  type?: 'info' | 'warn' | 'error' | 'success';
  title: string;
  desc: string;
  onOk?: () => void;
  onCancel?: () => void;
  onClose?: () => void;
  hasCancel?: boolean;
  okText?: string;
  cancelText?: string;
  descType?: 'middle' | 'full';
}

const ICON_MAP = {
  warn: 'https://gw.alicdn.com/imgextra/i3/O1CN010LbLZe1ji9NHtjSNP_!!6000000004581-2-tps-120-120.png',
  error:
    'https://gw.alicdn.com/imgextra/i1/O1CN01GMOtpJ1OM4Lhrlumy_!!6000000001690-2-tps-120-120.png',
  success: 'https://gw.alicdn.com/imgextra/i4/O1CN01SskjGh28t1DxqxMvG_!!6000000007989-2-tps-48-48.png',
};

const BasePopup = ({
  visible,
  setVisible,
  title,
  desc,
  type = 'info',
  hasCancel,
  onCancel,
  onClose,
  onOk,
  okText,
  cancelText,
  descType = 'middle',
}: PopupOptions) => {
  const handleClose = useCallback(() => {
    setVisible(false);
    onClose?.();
  }, [onClose, setVisible]);

  const handleOk = useCallback(() => {
    setVisible(false);
    onOk?.();
  }, [onOk, setVisible]);

  const handleCancel = useCallback(() => {
    setVisible(false);
    onCancel?.();
  }, [onCancel, setVisible]);

  return (
    <Popup visible={visible} onClose={handleClose}>
      <div className={styles.popup}>
        {type !== 'info' && <img src={ICON_MAP[type]} className={styles.icon} />}
        <div className={`${styles.title} ${type === 'info' && styles.noIconTitle}`}>{title}</div>
        <div
          className={`${styles.desc} ${descType === 'full' ? styles.descFull : styles.descMiddle}`}
        >
          {desc}
        </div>
        <div className={styles.actionArea}>
          {hasCancel && <Button onClick={handleCancel}>{cancelText || '取消'}</Button>}
          <Button color="primary" onClick={handleOk}>
            {okText || '确认'}
          </Button>
        </div>
        <SafeArea position="bottom" />
      </div>
    </Popup>
  );
};

export default BasePopup;
