.quitContainer {
  padding: 24rpx 32rpx;
}

.quitContainer .title {
  font-family: PingFang SC;
  line-height: 92rpx;
  margin-bottom: 16rpx;
  font-size: 30rpx;
  font-weight: 500;
  color: #111;
  margin-right: 12rpx;
}

.quitContainer .title span {
  font-family: <PERSON>Fang SC;
  color: #666;
  font-size: 26rpx;
}

.checkboxContainer {
  display: flex;
  flex-direction: column;
  overflow-y: scroll;
  max-height: calc(95vh - 300rpx);
}

.checkboxContainer .checkboxItem {
  display: flex;
  flex-direction: column;
  margin-bottom: 32rpx;
}

.quitContainer .actionArea {
  display: flex;
  justify-content: space-between;
  gap: 16rpx;
  padding-top: 14rpx;
  padding-bottom: 14rpx;
  button {
    flex: 1;
  }
}

.checkboxItem .formIcon {
  width: 36rpx;
  height: 36rpx;
}

.checkboxContainer .checkboxDesc {
  padding: 16rpx;
  background: #f7f8fa;
  font-size: 26rpx;
  border: 1px solid #e4e6ed;
  margin: 12rpx 0 26rpx 40rpx;
  display: flex;
  flex-direction: column;
  justify-content: center;
  gap: 16rpx;
}

.checkboxDesc .formItemExtra {
  display: flex;
  flex-direction: column;
  justify-content: center;
  background: #fff;
  padding-right: 24rpx;
  height: 100%;
  color: #666;
  font-family: PingFang SC;
  font-size: 26rpx;
  font-weight: normal;
}

.checkboxDesc input {
  background: #fff;
  border-radius: 6rpx;
  padding-left: 20rpx;
}
