.quitContainer {
  font-family: <PERSON><PERSON>ang SC;
  display: flex;
  flex-direction: column;
  height: 100%;
  padding: 34rpx 37rpx 16px;
  box-sizing: border-box;
  background-image: url('https://gw.alicdn.com/imgextra/i4/O1CN01ZYJspj1IN5aIDUJ6q_!!6000000000880-2-tps-750-846.png');
  background-size: 100% 100%;
  background-repeat: no-repeat;
}

.title {
  font-family: DingTalk JinBuTi;
  font-size: 36rpx;
  line-height: 54rpx;
  background: linear-gradient(95deg, #f27 0%, #e43f63 42%, #ac2249 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-fill-color: transparent;
  margin-bottom: 12rpx;
}

.desc {
  color: var(--text-secondary);
  font-size: 26rpx;
  line-height: 42rpx;
  color: #7a8599;
  margin-bottom: 12rpx;
}

.formIcon {
  width: 36rpx;
  height: 36rpx;
}

.content {
  flex: 1;
  overflow: hidden;

  .contentItemBox {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 32rpx 24rpx;
    margin-bottom: 16rpx;
    border-radius: 24rpx;
    background: radial-gradient(35% 42% at 9% 101%, rgba(248, 41, 113, 0.05) 0%, rgba(248, 41, 113, 0) 100%),
      linear-gradient(90deg, #fff 0%, rgba(255, 255, 255, 0) 100%);
    img {
      width: 247rpx;
      height: 162rpx;
    }
    .contentItem {
      .contentItemDesc {
        font-weight: 500;
        font-size: 26rpx;
        margin-right: 6px;
      }
      .contentItemExposure {
        color: #f52c6f;
        font-size: 32rpx;
      }
    }
  }
}

.actionArea {
  display: flex;
  justify-content: space-between;
  width: 100%;
  gap: 16rpx;
  padding-top: 14rpx;
  padding-bottom: 14rpx;

  button {
    flex: 1;
  }
}
