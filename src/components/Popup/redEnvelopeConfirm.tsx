import { <PERSON><PERSON>, Popup, SafeArea } from 'antd-mobile';
import { useEffect } from 'react';
import { RED_ENVELOPE_DESC } from '@/lib/constant';
import { money_US } from '@ali/iec-dtao-utils/esm/common/number';
import { log } from '@ali/iec-dtao-utils';

import styles from './redEnvelopeConfirm.module.css';

interface RedEnvelopeConfirmProps {
  visible: boolean;
  redEnvelopeInfo: any;
  setVisible: (visible: boolean) => void;
  onQuitBtn: () => void;
  onReceiveBtn: () => void;
}

const RedEnvelopeConfirm = ({ visible, setVisible, onQuitBtn, onReceiveBtn, redEnvelopeInfo }: RedEnvelopeConfirmProps) => {
  const { currentServiceFee, discountServiceFee, effectiveDays, showDiscount, canNotQuitDays } = redEnvelopeInfo;

  useEffect(() => {
    visible && log.addLog('red-envelope-popup-init', 'visit', { redEnvelopeInfo });
  }, [visible]);

  const onClose = () => {
    log.addLog('red-envelope-popup-close-click', 'success');
    setVisible(false);
  };

  const renderServiceFee = (amount) => {
    if (!String(amount).includes('.')) {
      return (
        <span className={styles.integerAmount}>
          <span className={styles.decimalAmount}>{amount}</span>
        </span>);
    }

    const [integerAmount, decimalAmount] = String(amount).split('.');

    return (
      <span className={styles.feeAmount}>
        <span className={styles.integerAmount}>{integerAmount}</span>.
        <span className={styles.decimalAmount}>{decimalAmount}</span>
      </span>
    );
  };

  return (
    <Popup visible={visible} showCloseButton onClose={onClose}>
      <div className={styles.redEnvelopeContainer}>
        <div className={styles.title}>退出前必领，{effectiveDays || '--'}天退货宝{showDiscount?.value || '--'}折优惠</div>
        <div className={styles.subTitle}>若不退出，您可领取{effectiveDays || '--'}天退货宝{showDiscount?.value || '--'}折优惠，领取优惠后{canNotQuitDays || '--'}天内无法退出保障</div>

        <div className={styles.container}>
          {/* 当前服务费 */}
          <div className={[styles.currentServiceFee, styles.serviceFee].join(' ')}>
            <div className={styles.title}>您的当前服务费</div>
            <div className={styles.currentAmount}>
              <span className={styles.symbol}>¥</span>
              <span>{renderServiceFee(money_US(currentServiceFee?.feeAmount?.value))}</span>
              <span className={styles.unit}>/单</span>
            </div>
            <div className={styles.originalAmount}>
              <span>¥{money_US(currentServiceFee?.originalFeeAmount?.value)}</span>
              <span>/单 </span>
            </div>
          </div>
          {/* 领取权益后预计的服务费 */}
          <div className={[styles.discountServiceFee, styles.serviceFee].join(' ')}>
            <span className={styles.title}>领取权益后预计的服务费</span>
            <div className={styles.discountAmount}>
              <span className={styles.symbol}>¥</span>
              <span>{renderServiceFee(money_US(discountServiceFee?.feeAmount?.value))}</span>
              <span className={styles.unit}>/单</span>
            </div>
            <div className={styles.originalAmount}>
              <span>¥{money_US(discountServiceFee?.originalFeeAmount?.value)}</span>
              <span>/单 </span>
            </div>
          </div>

        </div>

        {/* 说明 */}
        <div className={styles.desc}>
          <span>•</span>
          {RED_ENVELOPE_DESC}
        </div>

        {/* btn */}
        <div className={styles.actionArea}>
          <Button onClick={() => {
            log.addLog('red-envelope-popup-quit-click', 'success');
            onQuitBtn();
          }}
          >确认退出
          </Button>
          <Button
            onClick={() => {
              log.addLog('red-envelope-popup-receive-click', 'success');
              onReceiveBtn();
            }}
            color="warning"
            className={styles.footerBtn}
          >
            立即领取
          </Button>
        </div>
        <SafeArea position="bottom" />
      </div>
    </Popup>
  );
};

export default RedEnvelopeConfirm;
