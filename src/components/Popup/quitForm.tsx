import { Button, Checkbox, Popup, SafeArea, Input, Form } from 'antd-mobile';
import { useCallback, useEffect, useState } from 'react';
import { QUIT_REASON_LIST } from '@/lib/constant';
import { log } from '@alife/dtao-iec-spm-log';

import styles from './quitForm.module.css';

const CHECK_ICON =
  'https://gw.alicdn.com/imgextra/i1/O1CN014CTwdF1iIDKZktPH0_!!6000000004389-55-tps-36-36.svg';

const UN_CHECK_ICON =
  'https://gw.alicdn.com/imgextra/i2/O1CN01XrVpWP1b5ClqtAX2E_!!6000000003413-55-tps-36-36.svg';

interface PopupOptions {
  visible: boolean;
  setVisible: (visible: boolean) => void;
  onConfirm: (reason: string[]) => void;
}

const getIcon = (checked: boolean) => {
  return <img src={checked ? CHECK_ICON : UN_CHECK_ICON} className={styles.formIcon} />;
};

const QuitFormPopup = ({ visible, setVisible, onConfirm }: PopupOptions) => {
  const [inputValue, setInputValue] = useState({});

  const handleClose = useCallback(() => {
    log.addLog('quit-reason-close-click', 'success');
    setVisible(false);
  }, [setVisible]);

  const [reason, setReason] = useState<string[]>([]);
  const disableConfirm = !reason.length;

  useEffect(() => {
    setReason([]);
    visible && log.addLog('quit-reason-popup-init', 'visit');
  }, [visible]);

  return (
    <Popup visible={visible} bodyStyle={{ maxHeight: '95vh' }} showCloseButton onClose={handleClose}>
      <div className={styles.quitContainer}>
        <div className={styles.title}>您的建议，让我们做的更好<span>（可多选）</span></div>
        <div className={styles.formContainer}>
          <div className={styles.checkboxContainer}>
            <Checkbox.Group
              value={reason}
              onChange={(checkedList) => {
                log.addLog('quit-reason-select-item', 'success');
                setReason(checkedList as string[]);
              }}
            >
              {QUIT_REASON_LIST.map((item, index) => (
                <div className={styles.checkboxItem} key={index}>
                  <Checkbox
                    block
                    value={item.value}
                    icon={getIcon}
                    onChange={() => {
                      // 删除对应的input值
                      if (inputValue[index]) {
                        delete inputValue[index];
                      }
                    }}
                  >
                    <div> {item.label}</div>
                  </Checkbox>
                  {
                    reason.includes(item.value) && (item?.desc || item?.isInput) ?
                      <div className={styles.checkboxDesc}>
                        {item?.desc && <div>{item.desc}</div>}
                        {item?.isInput &&
                          <Form.Item
                            className={styles.formItem}
                            extra={<div className={styles.formItemExtra}>{inputValue[index]?.length || 0}/50</div>}
                          >
                            <Input
                              value={inputValue[index]}
                              placeholder="请输入"
                              clearable
                              onChange={(value) => {
                                const newValue = value?.slice(0, 50) || '';
                                inputValue[index] = newValue;
                                if (!newValue) {
                                  delete inputValue[index];
                                }
                                setInputValue({ ...inputValue });
                              }}
                            />
                          </Form.Item>
                        }
                      </div> : null
                  }
                </div>
              ))}
            </Checkbox.Group>
          </div>
        </div>

        <div className={styles.actionArea}>
          <Button onClick={() => onConfirm(reason.concat(Object.values(inputValue)))} disabled={disableConfirm}>
            确认退出
          </Button>
          <Button color="primary" onClick={handleClose}>我再想想</Button>
        </div>

        <SafeArea position="bottom" />
      </div>
    </Popup>
  );
};

export default QuitFormPopup;
