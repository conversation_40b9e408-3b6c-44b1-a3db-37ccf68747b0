import { Button, Popup, SafeArea } from 'antd-mobile';
import { useEffect } from 'react';
import { map } from 'lodash-es';
import { log } from '@alife/dtao-iec-spm-log';
import { INTEREST_LIST } from '@/lib/constant';

import styles from './interestConfirm.module.css';

interface InterestConfirmProps {
  visible: boolean;
  quitRetainPopupType: string;
  setVisible: (params: boolean) => void;
  onConfirm: () => void;
}

const retainPopupType = {
  forward: '+',
  backward: '-',
};

const InterestConfirm = ({ setVisible, visible, onConfirm, quitRetainPopupType }: InterestConfirmProps) => {
  useEffect(() => {
    visible && log.addLog('interest-popup-init', 'visit', { quitRetainPopupType });
  }, [visible]);

  return (
    <Popup
      visible={visible}
      showCloseButton
      closeOnMaskClick
      onClose={() => {
        log.addLog('interest-popup-close-click', 'success', { quitRetainPopupType });
        setVisible(false);
      }}
    >
      <div className={styles.quitContainer}>
        <div className={styles.title}>您确定要退出消费者体验提升计划吗？</div>
        <div className={styles.desc}>
          退出后，历史订单权益不受影响，新增订单将取消专属标识及运费保障权益。若您后续参与平台大促活动，可能影响活动准入资格。
        </div>

        <div className={styles.content}>
          {
            map(INTEREST_LIST, (item, index) => {
              return (
                <div className={styles.contentItemBox} key={index}>
                  <div className={styles.contentItem}>
                    <span className={styles.contentItemDesc}>{item.desc}</span>
                    <span className={styles.contentItemExposure}>{retainPopupType[quitRetainPopupType]}{item.exposure}%</span>
                  </div>
                  <img src={item.path} />
                </div>
              );
            })
          }
        </div>

        <div className={styles.actionArea}>
          <Button onClick={() => {
            log.addLog('interest-popup-ok-click', 'success', { quitRetainPopupType });
            onConfirm();
          }}
          >确认退出
          </Button>
          <Button
            color="primary"
            onClick={() => {
              log.addLog('interest-popup-cancel-click', 'success', { quitRetainPopupType });
              setVisible(false);
            }}
          >
            我再想想
          </Button>
        </div>
        <SafeArea position="bottom" />
      </div>
    </Popup>
  );
};

export default InterestConfirm;
