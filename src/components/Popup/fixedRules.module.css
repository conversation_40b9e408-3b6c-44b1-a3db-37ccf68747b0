.area {
  height: 812rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 0;
  z-index: 0;
  border-radius: 12rpx 12rpx 0 0;
  background: #fff;
}
.title {
  display: flex;
  height: 96rpx;
  z-index: 0;
  align-items: center;
}
.titleText {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 24rpx;
  flex-grow: 1;
  font-family: PingFang SC;
  font-size: 32rpx;
  font-weight: 500;
  color: #111;
}
.content {
  display: flex;
  flex-direction: column;
  justify-content: center;
  gap: 16rpx;
  align-self: stretch;
  z-index: 0;
  padding: 0 32rpx;
}
.contentIntro{
    
}
.contentTitle {
  font-family: PingFang SC;
  font-size: 26rpx;
  line-height: 40rpx;
  letter-spacing: normal;
  color: #111;
}
.contentIntro {
  font-weight: normal;
}
.contentTitle {
  display: flex;
  align-items: center;
  gap: 12rpx;
  z-index: 0;
  font-weight: 500;
}
