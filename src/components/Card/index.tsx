import styles from './index.module.css';

interface CardOptions {
  title: string;
  children?: React.ReactNode;
  extra?: React.ReactNode;
  className?: string;
  style?: React.CSSProperties;
}

const Card = ({ title, children, extra, className, style }: CardOptions) => {
  return (
    <div className={`${styles.card} ${className}`} style={style}>
      <div className={styles.headPanel}>
        <div className={styles.titleNode}>{title}</div>
        <div className={styles.extraNode}>{extra}</div>
      </div>
      {
        children && (
          <div className={styles.contentPanel}>
            {children}
          </div>
        )
      }
    </div>
  );
};

export default Card;
