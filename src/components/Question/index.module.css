.questionContent {
  display: flex;
  align-items: flex-start;
  line-height: 40rpx;

  font-size: var(--text-size-base);
  font-weight: var(--text-weight-xl);
}

.icon {
  width: 36rpx;
  margin-right: 8rpx;
  vertical-align: top;
  padding-top: 5.5rpx;
}

.answerContent {
  font-size: var(--text-size-base);
  font-weight: normal;
  line-height: 40rpx;
  color: var(--text-secondary);
}

.showMore {
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  padding-top: 24rpx;
  color: var(--text-size-base);
  font-size: var(--text-size-base);
  font-weight: normal;
  line-height: 40rpx;
}

.arrow {
  width: 24rpx;
}

.moreArrow {
  margin-left: 6rpx;
  rotate: -90deg;
}

.imageContainer {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16rpx;
  margin-top: 8rpx;
}

.image {
  width: 100%;
  height: auto;
  display: block;
  border-radius: 12rpx;
}
