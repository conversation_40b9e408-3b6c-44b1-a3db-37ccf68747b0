import { useCallback, useState } from 'react';
import styles from './index.module.css';
import { Button, SafeArea } from 'antd-mobile';
import AgreementPopup from '../Popup/agreements';
import BasePopup from '../Popup/base';
import { ORIGINAL_PRICE_DESC, PRICE_DESC, PRICE_TITLE } from '@/lib/constant';
import { log } from '@alife/dtao-iec-spm-log';
import { number } from '@ali/iec-dtao-utils';
import { getPriceDisplay } from '@/lib/tools';

const { money_US } = number;

interface BottomBarOptions {
  originalFeeAmount: string;
  discountFeeAmount?: string;
  marketTitle?: string | null;
  promotionFlag?: boolean;
  onOpenClick: () => void;
}

const QuestionIcon =
  'https://gw.alicdn.com/imgextra/i4/O1CN01GoG99Z1dkXkhCNZfV_!!6000000003774-55-tps-23-23.svg';

const BottomBar = ({
  originalFeeAmount,
  discountFeeAmount,
  onOpenClick,
  promotionFlag,
  marketTitle,
}: BottomBarOptions) => {
  const [agreementVisible, setAgreementVisible] = useState(false);
  const [pricePopupVisible, setPricePopupVisible] = useState(false);
  const [popupInfo, setPopupInfo] = useState<{
    title: string;
    desc: string;
    descType: 'middle' | 'full';
  }>({
    title: PRICE_TITLE,
    desc: PRICE_DESC,
    descType: 'middle',
  });

  const handleAgreementClick = useCallback(() => {
    setAgreementVisible(true);
  }, []);

  const handleQuestionClick = useCallback(() => {
    setPopupInfo({ title: PRICE_TITLE, desc: PRICE_DESC, descType: 'full' });
    setPricePopupVisible(true);
  }, []);

  const handleOriginalPriceQuestionClick = useCallback(() => {
    setPopupInfo({ title: '', desc: ORIGINAL_PRICE_DESC, descType: 'middle' });
    setPricePopupVisible(true);
  }, []);

  const handleOpen = useCallback(() => {
    onOpenClick();
  }, [onOpenClick]);

  // 使用统一的价格显示配置
  const priceDisplay = getPriceDisplay(originalFeeAmount, discountFeeAmount, promotionFlag);

  const renderFeeAmountContent = () => {
    if (priceDisplay.showOriginalPrice) {
      return (
        <div className={styles.priceMainContent}>
          <div className={styles.priceTitle}>
            预估服务费
            <img src={QuestionIcon} className={styles.questionIcon} onClick={handleQuestionClick} />
          </div>
          <div className={styles.discountPriceContainer}>
            <div className={styles.disCountPrice}>
              <span className={`${styles.priceNum} ${styles.discountPriceNum}`}>
                {money_US(priceDisplay.currentPrice || '0')}
              </span>
              <span> 元/单</span>
            </div>
            <div className={styles.originalPrice}>
              <span className={`${styles.priceNum} ${styles.originalPriceNum}`}>
                {money_US(priceDisplay.originalPrice)}
              </span>
              <span> 元/单</span>
              <img
                src={QuestionIcon}
                className={styles.questionIcon}
                onClick={handleOriginalPriceQuestionClick}
              />
            </div>
          </div>
        </div>
      );
    }
    return (
      <>
        <div className={styles.priceTitle}>
          预估服务费
          <img src={QuestionIcon} className={styles.questionIcon} onClick={handleQuestionClick} />
        </div>
        <div className={styles.priceNumCommonContainer}>
          <span className={styles.priceNum}>{money_US(priceDisplay.currentPrice)}</span>
          <span> 元/单</span>
        </div>
      </>
    );
  };

  const renderApplyBtn = ({
    renderPromotionFlag,
  }) => {
    return (
      <div className={styles.promotionBtn}>
        {
          marketTitle && (
            <div className={styles.promotionBtnTitle}>
              {marketTitle}
            </div>
          )
        }
        <Button
          color="primary"
          style={{ width: '358rpx' }}
          onClick={() => {
            log.addLog('open-click', 'click', { type: renderPromotionFlag ? 'promotion' : 'normal' });
            handleOpen();
          }}
        >
          同意协议并加入
        </Button>
      </div>
    );
  };

  return (
    <div className={styles.bottomBar}>
      <div className={styles.agreements}>
        请阅读并同意<a onClick={handleAgreementClick}>《消费者体验提升计划协议》</a>
      </div>
      <div className={styles.joinContainer}>
        <div className={`${styles.price} ${priceDisplay.showOriginalPrice ? styles.discount : ''}`}>
          {renderFeeAmountContent()}
        </div>
        {renderApplyBtn({
          renderPromotionFlag: promotionFlag,
        })}
      </div>
      <SafeArea position="bottom" />
      <AgreementPopup visible={agreementVisible} setVisible={setAgreementVisible} />
      <BasePopup
        visible={pricePopupVisible}
        setVisible={setPricePopupVisible}
        title={popupInfo.title}
        desc={popupInfo.desc}
        descType={popupInfo.descType}
      />
    </div>
  );
};

export default BottomBar;
