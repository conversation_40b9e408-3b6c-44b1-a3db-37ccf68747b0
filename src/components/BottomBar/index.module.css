.bottomBar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: white;
  padding: 0 32rpx;

  border-width: 1px 0 0 0;
  border-style: solid;
  border-color: var(--bg-secondary);
}

.agreements {
  padding-top: 16rpx;
  font-size: var(--text-size-base);

  a {
    color: var(--text-link);
  }
}

.joinContainer {
  padding: 16rpx 0;
  display: flex;
  justify-content: space-between;
}

.price {
  font-size: 24rpx;
  display: flex;
  align-items: center;
}

.discount {
  display: block;
}

.priceTitle {
  display: flex;
  align-items: center;
}

.questionIcon {
  width: 23.4rpx;
  margin-left: 4.3rpx;
}

.disCountPrice {
  display: inline-block;
  color: var(--text-price);
}

.discountPriceContainer {
  margin-top: 10rpx;
  font-family: "AlibabaFontMd";
}

.priceNumHalfContainer {
  font-family: "AlibabaFontMd";
  margin-left: 12rpx;
  color: var(--text-promotion-price);
}

.priceNumCommonContainer {
  font-family: "AlibabaFontMd";
  margin-left: 12rpx;
}

.priceNumContainer {
  font-family: "AlibabaFontMd";
  margin-left: 12rpx;
  color: var(--text-promotion-price);
}

.priceNumHalfContainer {
  font-size: 36rpx;
  line-height: 36rpx;
  font-weight: 500;
  color: var(--text-promotion-price);
}

.priceMainContent {
  display: flex;
  flex-direction: column;
}

.promotionHalfContentMain {
  display: flex;
  flex-direction: row;
}

.promotionHalfContentDesc {
  font-size: 24rpx;
  line-height: 100%;
  color: #999;
}

.promotionHalfBtn {
  display: flex;
  flex-direction: column;
  width: 358rpx;
  margin-top: -26rpx;
}

.promotionHalfTitle {
  position: relative;
  text-align: center;
  left: 20rpx;
  right: 0;
  top: 16rpx;
  z-index: 999;
  width: 325rpx;
  height: 26rpx;
  padding: 5rpx;
  text-align: center;
  border-radius: 40rpx;
  background-color: #ff3d3d;
  color: #fff;
  font-size: 20rpx;
}

.promotionBtn {
  position: relative;
  display: inline-block;
  flex-direction: column;
}

.promotionBtnTitle {
  position: absolute;
  right: 0;
  top: -26rpx;
  z-index: 1;
  width: auto;
  height: 32rpx;
  line-height: 32rpx;
  padding: 6rpx 12rpx;
  text-align: center;
  border-radius: 40rpx;
  background-color: #ff3d3d;
  color: #fff;
  font-size: 20rpx;
}

.priceNum {
  font-size: 36rpx;
  line-height: 36rpx;
  font-weight: 500;
  color: var(--text-base);
}

.discountPriceNum {
  color: var(--text-price);
}

.originalPrice {
  display: inline-flex;
  font-size: 24rpx;
  text-decoration: line-through;
  text-decoration-color: rgba(153, 153, 153);
  letter-spacing: 0;
  margin-left: 12rpx;
  align-items: center;
}

.originalPriceNum {
  font-size: 24rpx;
}
