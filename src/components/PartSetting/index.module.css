.partSettingPanel {
  padding: 32rpx 32rpx 0 32rpx;
  display: flex;
  flex-direction: column;
  height: 100%;
  box-sizing: border-box;
}

.title {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  font-size: 32rpx;
  font-weight: 500;
  line-height: 48rpx;
  margin-bottom: 36rpx;
  img {
    margin-top: 50rpx;
    width: 60rpx;
    height: 60rpx;
    background-repeat: no-repeat;
  }
}

.desc {
  margin-bottom: 40rpx;
  font-size: 26rpx;
  font-weight: normal;
  line-height: 40rpx;
  color: #666;
  span {
    font-weight: 500;
    color: #111;
  }
}

.recommendedTag {
  width: 72rpx;
  height: 36rpx;
  border-radius: 6rpx;
  background-color: rgba(255, 0, 0, 0.06);
  color: #f00;
  font-variation-settings: "opsz" auto;
  font-size: 24rpx;
  padding: 3px;
  font-weight: normal;
  line-height: 36rpx;
}

.partExtraDesc {
  color: #999;
}

.actionArea {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
}

:global(.adm-radio-content) {
  font-size: 26rpx;
  font-weight: normal;
  line-height: 40rpx;
  color: #111;
}

:global(.adm-radio) {
  margin-bottom: 32rpx;
}
