/**
* @file index
* @date 2024-12-17
* <AUTHOR>
*/

import { useEffect, useState } from 'react';
import styles from './index.module.css';
import { Button, Popup, Radio, SafeArea } from 'antd-mobile';
import { log } from '@alife/dtao-iec-spm-log';

const PartSetting = ({
  visible,
  giveType,
  setVisible,
  onChangeSetting,
}: {
  visible: boolean;
  giveType?: string | null;
  setVisible: (visible: boolean) => void;
  onChangeSetting: (option) => void;
}) => {
  const [option, setOption] = useState<string>(giveType ?? 'ALL');

  // 各弹框打点
  useEffect(() => {
    // 全店设置弹框曝光
    if (visible && (giveType === 'ALL' || giveType === null)) {
      log.addLog('part-setting-all', 'success');
    }
    // 部分设置弹框曝光
    if (visible && (giveType === 'PART')) {
      log.addLog('part-setting-part', 'success');
    }
  }, [visible, giveType]);

  useEffect(() => {
    setOption(giveType ?? 'ALL');
  }, [giveType]);

  useEffect(() => {
    !visible && setOption(giveType ?? 'ALL');
  }, [visible]);

  const handleOnChangeOption = (value) => {
    setOption(value);
  };

  return (
    <>
      <Popup visible={visible} showCloseButton onClose={() => { log.addLog('all-to-part-close', 'click'); setVisible(false); }} bodyStyle={{ height: '34vh' }}>
        <div className={styles.partSettingPanel}>
          <div className={styles.title}>
            服务设置
          </div>
          <Radio.Group onChange={handleOnChangeOption} value={option}>
            <Radio value={'ALL'}>全店开通退货宝<span className={styles.recommendedTag}>推荐</span></Radio>
            <Radio value={'PART'}>部分开通退货宝：针对精选用户提供服务
              <div className={styles.partExtraDesc}>
                (为保障消费者体验，本设置仅在日常生效，特殊时段如大促期间仍保持全店开通，感谢您的理解)
              </div>
            </Radio>
          </Radio.Group>
          <div className={styles.actionArea}>
            {
              option === 'ALL' && (
                <>
                  <Button
                    block
                    color="primary"
                    onClick={() => {
                      log.addLog('dialog-part-setting', 'click', { from: option });
                      onChangeSetting(option);
                      setVisible(false);
                    }}
                  >确认
                  </Button>
                </>
              )
            }
            {
              option === 'PART' && (
                <>
                  <Button
                    block
                    onClick={() => {
                      log.addLog('dialog-part-setting', 'click', { from: option });
                      onChangeSetting(option);
                      setVisible(false);
                    }}
                  >确认
                  </Button>
                  <Button
                    block
                    color="primary"
                    onClick={() => {
                      log.addLog('dialog-part-setting-cancel', 'click', { from: giveType });
                      setVisible(false);
                    }}
                    style={{ marginLeft: '16rpx' }}
                  >取消
                  </Button>
                </>
              )
            }
          </div>
        </div>
        <SafeArea position="bottom" />
      </Popup>
    </>
  );
};

export default PartSetting;
