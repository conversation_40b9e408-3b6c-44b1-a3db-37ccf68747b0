import { CLOSE_ICON } from '@/lib/constant';
import { Mask } from 'antd-mobile';

import styles from './index.module.css';

interface CustomPopupProps {
  visible: boolean;
  imageUrl: string;
  onMaskClick: () => void;
  onClose: () => void;
  onImgClick: () => void;
}

const CustomPopup = (props: CustomPopupProps) => {
  const {
    visible = false,
    imageUrl = '',
    onMaskClick = () => { },
    onClose = () => { },
    onImgClick = () => { },
  } = props;

  return (
    <>
      {visible && (
        <Mask
          className={styles['delivery-overlay']}
          visible={visible}
          onMaskClick={() => onMaskClick()}
        >
          <div className={styles['advertising-popup']}>
            <img
              src={imageUrl}
              className={styles['advertising-img']}
              onClick={() => onImgClick()}
            />
            <img
              src={CLOSE_ICON}
              className={styles['close-icon']}
              alt=""
              onClick={() => onClose()}
            />
          </div>
        </Mask>
      )}
    </>
  );
};

export default CustomPopup;
