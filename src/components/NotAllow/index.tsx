import styles from './index.module.css';

const IMG_SRC =
  'https://gw.alicdn.com/imgextra/i1/O1CN01nQ2rHH1N77oH6638n_!!6000000001522-2-tps-720-600.png';


const NotAllow = ({
  code,
  alipayLoginId,
}: {
  code?: string | null;
  alipayLoginId?: string | null;
}) => {
  const renderUnAdmittedContent = ({ rejectCode, alipay }) => {
    switch (rejectCode) {
      case 'ARREARS':
        return <>当前您存在退货宝服务费欠费，暂时无法开通，建议暂停发货15分钟后前往账房查看欠费金额，并向支付宝账户{alipay || '-'}足额充值，结清欠款后即可重新开通</>;
      case 'OTHER':
        return <>建议您加强店铺经营，控制退货率</>;
      default:
        return <>建议您加强店铺经营，控制退货率</>;
    }
  };

  return (
    <div className={styles.notAllow}>
      <img src={IMG_SRC} className={styles.img} />
      <div className={styles.title}>暂不符合准入要求</div>
      <div className={styles.subTitle}>{renderUnAdmittedContent({ rejectCode: code, alipay: alipayLoginId })}</div>
    </div>
  );
};

export default NotAllow;
