import { useEffect, useCallback, useState, useMemo } from 'react';
import { STATUS_TIPS_INFO } from '@/lib/constant';

enum ENoticeType {
  alert = 'alert',
  error = 'error',
}

interface TipItem {
  type: ENoticeType;
  content: string;
}

const useTips = ({
  status,
  isOpenFixed,
  isArrears,
  customerFreezeCode,
}) => {
  const [tips, setTips] = useState<TipItem | null>(null);

  const handleOnGetTips = useCallback(() => {
    let newTips: TipItem | null = null;

    switch (status) {
      case 'MERCHANT_FROZEN': // 冻结中
        if (customerFreezeCode) {
          newTips = (STATUS_TIPS_INFO[`${status}_${customerFreezeCode}`] || STATUS_TIPS_INFO[status]);
        } else {
          newTips = (STATUS_TIPS_INFO[status]);
        }
        break;
      case 'MERCHANT_QUITTING': // 退出中
        newTips = (STATUS_TIPS_INFO[status]);
        break;
      case 'MERCHANT_OPEN_SUCCEED': // 保障中
        if (isArrears) { // 欠费
          newTips = ({
            type: ENoticeType.error,
            content: '您的账户已欠费，请尽快至【千牛PC端—财务—总览—欠费金额】缴纳欠款。',
          });
        }
        break;
      default:
        newTips = null;
        break;
    }

    setTips(newTips);
  }, [status, isOpenFixed, customerFreezeCode, isArrears]);

  useEffect(() => {
    handleOnGetTips();
  }, [handleOnGetTips]);

  return useMemo(() => tips, [tips]);
};

export default useTips;
