import styles from './index.module.css';
import dayjs from 'dayjs';
import { money_US } from '@ali/iec-dtao-utils/esm/common/number';
import FixedRules from '../Popup/fixedRules';
import { useState } from 'react';
import BasePopup from '../Popup/base';
import { ORIGINAL_PRICE_DESC, ORIGINAL_PRICE_TITLE } from '@/lib/constant';
import { PriceInfo } from '@/types';
import { getPriceDisplay, getPriceTypeConfig, generateDynamicStyles } from '@/lib/tools';

interface FixedOpenCardProps {
  fixedInfoArray: PriceInfo[]; // 固定价信息数组
  onFixedPriceClick: (fixedIndex: number) => void; // 点击开通，传递选中的固定价索引
}

const QuestionIcon =
  'https://gw.alicdn.com/imgextra/i4/O1CN01GoG99Z1dkXkhCNZfV_!!6000000003774-55-tps-23-23.svg';


const FixedOpenCard = ({
  fixedInfoArray,
  onFixedPriceClick,
}: FixedOpenCardProps) => {
  const [popupVisible, setPopupVisible] = useState(false);
  const [currentFixedPriceType, setCurrentFixedPriceType] = useState<string>('');
  const [questionVisible, setQuestionVisible] = useState(false);
  const [questionInfo, setQuestionInfo] = useState<{
    title: string;
    desc: string;
    descType: 'middle' | 'full';
  }>({
    title: ORIGINAL_PRICE_TITLE,
    desc: ORIGINAL_PRICE_DESC,
    descType: 'middle',
  });

  // 日期格式化
  const checkTime = (time, formatRule) => (time && formatRule ? dayjs(time).format(formatRule) : '--');
  // 过滤出准入的固定价选项
  const accessibleFixedInfo = fixedInfoArray.filter((item) => item.isAccess);
  if (accessibleFixedInfo.length === 0) {
    return null; // 如果没有准入的固定价，不渲染
  }

  return (
    <>
      {accessibleFixedInfo.map((fixedInfo, index) => {
        // 获取配置信息
        const openConfig = getPriceTypeConfig('fixed', fixedInfo.fixedPriceType);
        const priceDisplay = getPriceDisplay(fixedInfo.feeAmount, fixedInfo.discountFeeAmount, fixedInfo.promotionFlag);
        const dynamicStyles = generateDynamicStyles(openConfig);
        const originalIndex = fixedInfoArray.findIndex((item) => item === fixedInfo);

        return (
          <div key={`${fixedInfo.fixedPriceType}-${index}`} className={styles.pricingCardContainer} style={dynamicStyles}>
            <div className={styles.pricingCardContent}>
              <div className={styles.privilegeTag}>
                <span className={styles.privilegeText}>{openConfig.privilegeText}</span>
              </div>
              <span className={styles.cardTitle}>{openConfig.title}</span>
              <div className={styles.priceContainer}>
                <span className={styles.priceAmount}>{money_US(priceDisplay.currentPrice || '0')}</span>
                <div className={styles.priceDetails}>
                  <span className={styles.priceUnit}>元/单</span>
                </div>
                {priceDisplay.showOriginalPrice && (
                  <>
                    <span className={styles.originalPrice}>
                      <span className={styles.originalAmount}>{money_US(priceDisplay.originalPrice)}</span>
                      <span className={styles.originalUnit}>元/单</span>
                    </span>
                    <img
                      src={QuestionIcon}
                      className={styles.questionIcon}
                      onClick={() => {
                        setQuestionInfo({
                          title: ORIGINAL_PRICE_TITLE,
                          desc: ORIGINAL_PRICE_DESC,
                          descType: 'middle',
                        });
                        setQuestionVisible(true);
                      }}
                    />
                  </>
                )}
              </div>
              <div className={styles.validityContainer}>
                <div className={styles.validityPeriod}>
                  <span className={styles.validityDates}>
                    {`有效期  ${checkTime(fixedInfo.effectTime, 'YYYY-MM-DD')}~${checkTime(fixedInfo.expireTime, 'YYYY-MM-DD')}`}
                  </span>
                </div>
                <span className={styles.commitmentText}>承诺在有效期内不退出</span>
              </div>
              <div className={styles.activateButton} onClick={() => { onFixedPriceClick(originalIndex); }}>
                <span className={styles.activateText}>立即开通</span>
              </div>
              <span
                className={styles.rulesLink}
                onClick={() => {
                  setCurrentFixedPriceType(fixedInfo.fixedPriceType || '');
                  setPopupVisible(true);
                }}
              >活动规则
              </span>
            </div>
          </div>
        );
      })}

      <FixedRules
        visible={popupVisible}
        onClose={() => { setPopupVisible(false); }}
        fixedPriceType={currentFixedPriceType}
      />
      <BasePopup
        visible={questionVisible}
        setVisible={setQuestionVisible}
        title={questionInfo.title}
        desc={questionInfo.desc}
        descType={questionInfo.descType}
      />
    </>
  );
};

export default FixedOpenCard;
