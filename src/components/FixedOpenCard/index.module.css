.pricingCardContainer {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  box-sizing: border-box;
}

.pricingCardContent {
  background: linear-gradient(180deg, var(--gradient-from, #fff7ed) 0%, var(--gradient-to, #fff0f0) 100%);
  border-radius: 12rpx;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-self: flex-start;
  width: 686rpx;
  padding: 32rpx 12rpx;
}

.questionIcon {
  display: flex;
  height: 24rpx;
  width: 28rpx;
}

.privilegeTag {
  background: var(--privilege-tag-background);
  border-radius: 12rpx;
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-self: center;
  height: 36rpx;
  padding: 0 12rpx;
  box-sizing: border-box;
}

.privilegeText {
  color: var(--theme-color, #c45902);
  font-size: 24rpx;
  font-weight: 500;
  line-height: 36rpx;
  align-self: flex-start;
  white-space: nowrap;
  box-sizing: border-box;
}

.cardTitle {
  color: #111;
  font-size: 26rpx;
  font-weight: 500;
  line-height: 30rpx;
  margin-top: 16rpx;
  align-self: center;
  max-width: 662rpx;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  box-sizing: border-box;
}

.priceContainer {
  display: flex;
  flex-direction: row;
  margin-top: 12rpx;
  gap: 3rpx;
  justify-content: center;
  align-items: flex-end;
  align-self: center;
  box-sizing: border-box;
}

.priceAmount {
  color: #111;
  font-size: 48rpx;
  font-weight: 500;
  line-height: 48rpx;
  font-family: "AlibabaFontMd";
  white-space: nowrap;
  box-sizing: border-box;
}

.priceDetails {
  display: flex;
  flex-direction: row;
  align-items: flex-end;
  gap: 2rpx;
  box-sizing: border-box;
}

.priceUnit {
  font-family: PingFang SC;
  font-size: 24rpx;
  font-weight: normal;
  line-height: 24rpx;
  letter-spacing: normal;
  color: #111;
  margin-bottom: 4rpx;
}

.originalPrice {
  font-size: 24rpx;
  font-weight: normal;
  display: flex;
  align-items: flex-end;
  letter-spacing: normal;
  color: #666;
  margin-bottom: 4rpx;
}

.originalAmount {
  color: #666;
  font-size: 24rpx;
  word-wrap: break-word;
  font-weight: 400;
  line-height: 24rpx;
  text-decoration: line-through;
}

.originalUnit {
  color: #666;
  font-size: 24rpx;
  word-wrap: break-word;
  font-weight: 400;
  line-height: 24rpx;
  text-decoration: line-through;
}

.validityContainer {
  display: flex;
  flex-direction: row;
  margin-top: 16rpx;
  justify-content: center;
  align-self: center;
  box-sizing: border-box;
}

.validityPeriod {
  color: #666;
  font-size: 24rpx;
  font-weight: 400;
  line-height: 28rpx;
  align-self: center;
  white-space: nowrap;
  box-sizing: border-box;
}

.validityLabel {
  color: #666;
  font-size: 24rpx;
  word-wrap: break-word;
  font-weight: 400;
  line-height: 24rpx;
}

.validityDates {
  color: #666;
  font-size: 24rpx;
  word-wrap: break-word;
  font-weight: 400;
  line-height: 24rpx;
}

.commitmentText {
  color: #666;
  font-size: 24rpx;
  font-weight: 400;
  line-height: 28rpx;
  margin-left: 12rpx;
  align-self: center;
  white-space: nowrap;
  box-sizing: border-box;
}

.activateButton {
  background: var(--button-background, linear-gradient(135deg, #ff6a00 0%, #c45902 100%));
  border-radius: 12rpx;
  display: flex;
  flex-direction: row;
  margin-top: 32rpx;
  justify-content: center;
  align-self: center;
  width: 622rpx;
  height: 72rpx;
  padding: 16rpx 12rpx;
  box-sizing: border-box;
}

.activateText {
  color: var(--theme-color);
  font-size: 26rpx;
  text-align: center;
  font-weight: 500;
  line-height: 40rpx;
  align-self: flex-start;
  max-width: 598rpx;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  box-sizing: border-box;
}

.rulesLink {
  color: #666;
  font-size: 24rpx;
  text-align: center;
  font-weight: 400;
  line-height: 36rpx;
  margin-top: 12rpx;
  align-self: center;
  max-width: 662rpx;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  box-sizing: border-box;
}

.questionIcon {
  width: 24rpx;
  height: 24rpx;
  margin-bottom: 4rpx;
  margin-left: 4rpx;
}