import { Popup } from 'antd-mobile';
import styles from './index.module.css';
import { money_US } from '@ali/iec-dtao-utils/esm/common/number';

interface CouponDetailProps {
  couponTitle?: string;
  couponSubTitle?: string;
  conditionDescription?: string;
  rule?: string[];
  totalPromotionQuota: any;
  maxUseTimes: string | number;
}

const renderLimitText = (item: any) => {
  const text: string[] = [];
  if (item?.totalPromotionQuota?.value) {
    text.push(`【限制使用额度${money_US(item?.totalPromotionQuota?.value) || '-'}元】`);
  }
  if (item?.maxUseTimes && Number(item.maxUseTimes) > 0) {
    text.push(`【限制核销${item.maxUseTimes}次以内】`);
  }
  if (text?.length) {
    return text.join('');
  }
  return null;
};
interface CouponDetailComponentProps {
  visible: boolean;
  couponData: CouponDetailProps | null;
  onClose: () => void;
}

const CouponDetail = ({ visible, couponData, onClose }: CouponDetailComponentProps) => {
  return (
    <Popup
      visible={visible}
      onMaskClick={onClose}
      onClose={onClose}
      showCloseButton
      className={styles.popup}
      bodyStyle={{
        borderTopLeftRadius: '12px',
        borderTopRightRadius: '12px',
      }}
    >
      <div className={styles.detailContainer}>
        <div className={styles.handle}></div>
        <div className={styles.titleArea}>
          <div className={styles.couponTitle}>{couponData?.couponTitle}</div>
          <div className={styles.couponDesc}>
            {couponData?.couponSubTitle ? `${couponData?.couponSubTitle}，` : ''}
            {couponData?.conditionDescription || '-'}
            {renderLimitText(couponData) ?? ''}
          </div>
        </div>
        <div className={styles.ruleArea}>
          <div className={styles.ruleTitle}>使用规则</div>
          {couponData?.rule && couponData?.rule.map((item, index) => {
            return (
              <div key={index} className={styles.ruleDesc}>
                {index + 1}. {item}
              </div>
            );
          })}
        </div>
        <div className={styles.otherArea}>
          <div className={styles.otherTitle}>其他注意事项</div>
          <div className={styles.otherDesc}>若在获取或者使用过程中，如存在违规行为（如作弊领取、恶意套现、刷取信誉、虚假交易等），上海淘天商业管理有限公司有权取消您的优惠券使用资格，若已使用上海淘天商业管理有限公司有权追回；如给上海淘天商业管理有限公司造成损失或不良影响的，上海淘天商业管理有限公司保留追究赔偿的权利。活动期间如出现不可抗力或情势变更的情况，上海淘天商业管理有限公司无需为此承担赔偿责任或进行补偿。</div>
        </div>
      </div>
    </Popup>
  );
};

export default CouponDetail;
