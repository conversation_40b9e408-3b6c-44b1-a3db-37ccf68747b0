.popup {
  --z-index: 1000;
}

.detailContainer {
  display: flex;
  flex-direction: column;
  padding: 0 30rpx 30rpx;
  gap: 40rpx;
  max-height: 80vh;
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;
}

.handle {
  width: 40px;
  height: 4px;
  background-color: #e0e0e0;
  border-radius: 2px;
  margin: 12px auto;
}

.titleArea {
  display: flex;
  flex-direction: column;
  padding: 0rpx;
  gap: 16rpx;
  margin-top: 10rpx;
}

.couponTitle,
.ruleTitle,
.otherTitle {
  font-size: 28rpx;
  font-weight: 500;
  line-height: 40rpx;
  letter-spacing: normal;
  color: #111;
}

.couponDesc,
.ruleDesc,
.otherDesc {
  font-size: 26rpx;
  font-weight: normal;
  line-height: 40rpx;
  letter-spacing: normal;
  color: #666;
}

.ruleArea,
.otherArea {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.ruleDesc {
  margin-bottom: 8rpx;
}

.otherDesc {
  text-align: justify;
  line-height: 1.6;
}

.ruleArea,
.otherArea {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}