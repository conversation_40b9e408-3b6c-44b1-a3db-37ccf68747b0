.detailCard {
  display: flex;
  gap: 56rpx;
  flex-direction: column;
}
.row {
  font-size: 26rpx;
  display: flex;
  width: 100%;
  flex-direction: column;
  gap: 8rpx;
}

.rowMain {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.title {
  font-weight: 500;
}

.desc {
  font-size: 24rpx;
  color: var(--text-secondary);
}

.descIcon {
  display: flex;
  flex-direction: row;
  align-items: center;
  font-size: 24rpx;
  color: #f00;
  img {
    width: 30rpx;
    height: 22rpx;
    background-size: 30rpx 22rpx;
    margin-right: 7rpx;
  }
}

.promotion {
  font-size: 24rpx;
  color: var(--text-secondary);
  margin-left: 12rpx;
  text-decoration: line-through;
  text-decoration-color: rgba(153, 153, 153);
}

.questionIcon {
  width: 28rpx;
  margin-left: 12rpx;
}

.promotionHalfDesc {
  color: #ff5000;
}

.questionIconSmall {
  width: 20rpx;
  margin-left: 6rpx;
}

.content {
  display: flex;
  align-items: center;
}

.aliPayIcon {
  width: 28rpx;
  margin-right: 12rpx;
}
