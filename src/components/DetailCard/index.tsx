/* eslint-disable @typescript-eslint/indent */
import dayjs from 'dayjs';
import styles from './index.module.css';
import { number } from '@ali/iec-dtao-utils';
import BasePopup from '../Popup/base';
import { ORIGINAL_PRICE_DESC, PRICE_DESC, PRICE_TITLE } from '@/lib/constant';
import { useCallback, useState } from 'react';
import { PriceInfo } from '@/types';
import { getPriceDisplay } from '@/lib/tools';

const { money_US } = number;

const AliPayIcon =
  'https://gw.alicdn.com/imgextra/i1/O1CN01QluZ3T25mCQNORgVi_!!6000000007568-2-tps-56-56.png';

const QuestionIcon =
  'https://gw.alicdn.com/imgextra/i4/O1CN01GoG99Z1dkXkhCNZfV_!!6000000003774-55-tps-23-23.svg';

interface DetailCardProps {
  alipayLoginId: string;
  status?: string;
  isOpenFixed?: boolean; // 是否开固定价
  priceInfo?: PriceInfo; // 价格信息（根据isOpenFixed判断是日常价还是固定价）
  marketIcon?: string | null; // 用增icon
}

const getBlock = (title, content, descInfo, icon, market) => {
  return (
    <div className={styles.row}>
      <div className={styles.rowMain}>
        <div className={styles.title}>{title}</div>
        <div className={`${styles.content}`}>{content}</div>
      </div>
      {
        descInfo && <div className={styles.desc}>{descInfo}</div>
      }
      {
        icon && market && (
          <div className={styles.descIcon}>
            <img src={icon} />{market}
          </div>
        )
      }
    </div>
  );
};

interface PriceBlockInfo {
  baseFeeAmount?: string; // 日常试算金额
  fixedFeeAmount?: string; // 固定价试算金额
  baseDiscountFeeAmount?: string; // 日常折扣价格
  fixedDiscountFeeAmount?: string; // 固定价折扣价格
  fixedPromotionFlag?: boolean; // 固定价是否有优惠
  basePromotionFlag?: boolean; // 是否营销
  marketIcon?: string | null; // 用增icon
  marketTitle?: string | null; // 用增标题
  isOpenFixed?: boolean;
  handleQuestionClick: () => void;
  handleOriginalPriceQuestionClick: () => void;
}

const getPriceBlock = ({
  baseFeeAmount,
  fixedFeeAmount,
  baseDiscountFeeAmount,
  fixedDiscountFeeAmount,
  fixedPromotionFlag,
  basePromotionFlag,
  marketIcon,
  marketTitle,
  isOpenFixed,
  handleQuestionClick,
  handleOriginalPriceQuestionClick,
}: PriceBlockInfo) => {
  // 一口价，没有问号
  if (isOpenFixed && fixedFeeAmount) {
    const priceDisplay = getPriceDisplay(fixedFeeAmount, fixedDiscountFeeAmount, fixedPromotionFlag);

    return getBlock(
      <span className={styles.content}>
        <span>当前服务费</span>
      </span>,
      <>
        <span>{money_US(priceDisplay.currentPrice || '0')} 元/单</span>
        {priceDisplay.showOriginalPrice && (
        <>
          <span className={styles.promotion}>{money_US(priceDisplay.originalPrice)} 元/单</span>
          <img
            src={QuestionIcon}
            className={styles.questionIconSmall}
            onClick={handleOriginalPriceQuestionClick}
          />
        </>
      )}
      </>,
     null,
     null,
     null,
);
  }

  // 日常价格
  if (!baseFeeAmount) return null;
  const priceDisplay = getPriceDisplay(baseFeeAmount, baseDiscountFeeAmount, basePromotionFlag);

  return getBlock(
    <span className={styles.content}>
      <span>预估服务费</span>
      <img src={QuestionIcon} className={styles.questionIcon} onClick={handleQuestionClick} />
    </span>,
    <>
      <span>{money_US(priceDisplay.currentPrice)} 元/单</span>
      {priceDisplay.showOriginalPrice && (
        <>
          <span className={styles.promotion}>{money_US(priceDisplay.originalPrice)} 元/单</span>
          <img
            src={QuestionIcon}
            className={styles.questionIconSmall}
            onClick={handleOriginalPriceQuestionClick}
          />
        </>
      )}
    </>,
    null,
    priceDisplay.showOriginalPrice ? marketIcon : null,
    priceDisplay.showOriginalPrice ? marketTitle : null,
  );
};

// 获取有效期标题
const getValidityTitle = (fixedPriceType?: string) => {
  switch (fixedPriceType) {
    case 'YEAR':
      return '年框有效期';
    case 'HALF_YEAR':
      return '半年框有效期';
    case 'SEASON':
      return '季框有效期';
    case 'MONTH':
      return '月框有效期';
    default:
      return '有效期';
  }
};

const DetailCard = ({ alipayLoginId, status, isOpenFixed, priceInfo, marketIcon }: DetailCardProps) => {
  const [pricePopupVisible, setPricePopupVisible] = useState(false);
  const [popupInfo, setPopupInfo] = useState<{
    title: string;
    desc: string;
    descType: 'middle' | 'full';
  }>({
    title: PRICE_TITLE,
    desc: PRICE_DESC,
    descType: 'middle',
  });

  const handleQuestionClick = useCallback(() => {
    setPopupInfo({ title: PRICE_TITLE, desc: PRICE_DESC, descType: 'full' });
    setPricePopupVisible(true);
  }, []);

  const handleOriginalPriceQuestionClick = useCallback(() => {
    setPopupInfo({ title: '', desc: ORIGINAL_PRICE_DESC, descType: 'middle' });
    setPricePopupVisible(true);
  }, []);

  // 时间信息：已开通固定价时使用 priceInfo 的时间，未开通时不显示时间
  const timeBlock =
    isOpenFixed && priceInfo?.effectTime && priceInfo?.expireTime
      ? getBlock(
        getValidityTitle(priceInfo?.fixedPriceType),
        `${dayjs(priceInfo?.effectTime).format('YYYY.MM.DD')} - ${dayjs(priceInfo?.expireTime).format('YYYY.MM.DD')}`,
        null,
        null,
        null,
      )
      : null;

  const priceBlock =
    status !== 'MERCHANT_QUITTING' && status !== 'MERCHANT_FROZEN' && status
      ? getPriceBlock({
        baseFeeAmount: !isOpenFixed ? priceInfo?.feeAmount : undefined,
        fixedFeeAmount: isOpenFixed ? priceInfo?.feeAmount : undefined,
        baseDiscountFeeAmount: !isOpenFixed ? priceInfo?.discountFeeAmount : undefined,
        fixedDiscountFeeAmount: isOpenFixed ? priceInfo?.discountFeeAmount : undefined,
        fixedPromotionFlag: isOpenFixed ? priceInfo?.promotionFlag : false,
        basePromotionFlag: !isOpenFixed ? priceInfo?.promotionFlag : false,
        marketIcon,
        marketTitle: priceInfo?.marketTitle,
        isOpenFixed,
        handleQuestionClick,
        handleOriginalPriceQuestionClick,
      })
      : null;

  return (
    <div className={styles.detailCard}>
      <div className={styles.row}>
        <div className={styles.rowMain}>
          <div className={styles.title}>保障内容</div>
          <div className={styles.content}>退换货保障首重运费</div>
        </div>
      </div>
      {priceBlock}
      <div className={styles.row}>
        <div className={styles.rowMain}>
          <div className={styles.title}>扣费账户</div>
          <div className={styles.content}>
            <img src={AliPayIcon} className={styles.aliPayIcon} />
            <span className={styles.aliPayLoginId}>{alipayLoginId}</span>
          </div>
        </div>
      </div>
      {timeBlock}

      <BasePopup
        visible={pricePopupVisible}
        setVisible={setPricePopupVisible}
        title={popupInfo.title}
        desc={popupInfo.desc}
        descType={popupInfo.descType}
      />
    </div>
  );
};

export default DetailCard;
