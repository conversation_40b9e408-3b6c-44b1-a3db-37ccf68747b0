/* eslint-disable @typescript-eslint/indent */
import dayjs from 'dayjs';
import styles from './index.module.css';
import { number } from '@ali/iec-dtao-utils';
import BasePopup from '../Popup/base';
import { ORIGINAL_PRICE_DESC, PRICE_DESC, PRICE_TITLE } from '@/lib/constant';
import { useCallback, useState } from 'react';

const { money_US } = number;

const AliPayIcon =
  'https://gw.alicdn.com/imgextra/i1/O1CN01QluZ3T25mCQNORgVi_!!6000000007568-2-tps-56-56.png';

const QuestionIcon =
  'https://gw.alicdn.com/imgextra/i4/O1CN01GoG99Z1dkXkhCNZfV_!!6000000003774-55-tps-23-23.svg';

export interface DetailInfo {
  status: string;
  isOpenFixed?: boolean; // 是否开固定价
  effectTime: number | null; // 固定价生效时间
  expireTime: number | null; // 固定价失效时间
  baseFeeAmount?: string; // 日常试算金额
  fixedFeeAmount?: string; // 固定价试算金额
  discountFeeAmount?: string; // 折扣价格
  promotionFlag?: boolean; // 是否营销
  promotionHalfOff?: boolean; // 是否5折
  promotionTitle?: string; // 营销标题
  marketIcon?: string | null; // 用增标题
  marketTitle?: string | null; // 用增标题
}

interface DetailCardProps {
  alipayLoginId: string;
  detailInfo?: DetailInfo;
}
const getBlock = (title, content, descInfo, icon, market) => {
  return (
    <div className={styles.row}>
      <div className={styles.rowMain}>
        <div className={styles.title}>{title}</div>
        <div className={`${styles.content}`}>{content}</div>
      </div>
      {
        descInfo && <div className={styles.desc}>{descInfo}</div>
      }
      {
        icon && market && (
          <div className={styles.descIcon}>
            <img src={icon} />{market}
          </div>
        )
      }
    </div>
  );
};

interface PriceBlockInfo {
  baseFeeAmount?: string; // 日常试算金额
  fixedFeeAmount?: string; // 固定价试算金额
  discountFeeAmount?: string; // 折扣价格
  promotionFlag?: boolean; // 是否营销
  promotionHalfOff?: boolean; // 是否5折
  promotionTitle?: string; // 营销标题
  marketIcon?: string | null; // 用增icon
  marketTitle?: string | null; // 用增标题
  isOpenFixed?: boolean;
  handleQuestionClick: () => void;
  handleOriginalPriceQuestionClick: () => void;
}

const getPriceBlock = ({
  baseFeeAmount,
  fixedFeeAmount,
  discountFeeAmount,
  promotionFlag,
  promotionHalfOff,
  promotionTitle,
  marketIcon,
  marketTitle,
  isOpenFixed,
  handleQuestionClick,
  handleOriginalPriceQuestionClick,
}: PriceBlockInfo) => {
  // 一口价，没有问号
  if (isOpenFixed && fixedFeeAmount) {
    return getBlock('当前服务费', `${money_US(fixedFeeAmount)} 元/单`, null, null, null);
  }

  // 5折
  if (promotionHalfOff && promotionTitle) {
    return getBlock(
      <span className={styles.content}>
        <span>预估服务费</span>
        <img src={QuestionIcon} className={styles.questionIcon} onClick={handleQuestionClick} />
      </span>,
      <>
        <span>{money_US(baseFeeAmount)} 元/单</span>
      </>,
      <>双11期间，在此服务费基础上您将享受前<span className={styles.promotionHalfDesc}>1111单5折</span>优惠</>,
      null,
      null,
    );
  }

  return getBlock(
    <span className={styles.content}>
      <span>预估服务费</span>
      <img src={QuestionIcon} className={styles.questionIcon} onClick={handleQuestionClick} />
    </span>,
    <>
      <span>{money_US(discountFeeAmount)} 元/单</span>
      {promotionFlag && (
        <>
          <span className={styles.promotion}>{money_US(baseFeeAmount)} 元/单</span>
          <img
            src={QuestionIcon}
            className={styles.questionIconSmall}
            onClick={handleOriginalPriceQuestionClick}
          />
        </>
      )}
    </>,
    null,
    promotionFlag ? marketIcon : null,
    promotionFlag ? marketTitle : null,
  );
};

const DetailCard = ({ alipayLoginId, detailInfo }: DetailCardProps) => {
  const {
    isOpenFixed,
    effectTime,
    expireTime,
    baseFeeAmount,
    fixedFeeAmount,
    discountFeeAmount,
    promotionFlag,
    promotionHalfOff,
    promotionTitle,
    marketIcon,
    marketTitle,
    status,
  } = detailInfo || {};

  const [pricePopupVisible, setPricePopupVisible] = useState(false);
  const [popupInfo, setPopupInfo] = useState<{
    title: string;
    desc: string;
    descType: 'middle' | 'full';
  }>({
    title: PRICE_TITLE,
    desc: PRICE_DESC,
    descType: 'middle',
  });

  const handleQuestionClick = useCallback(() => {
    setPopupInfo({ title: PRICE_TITLE, desc: PRICE_DESC, descType: 'full' });
    setPricePopupVisible(true);
  }, []);

  const handleOriginalPriceQuestionClick = useCallback(() => {
    setPopupInfo({ title: '', desc: ORIGINAL_PRICE_DESC, descType: 'middle' });
    setPricePopupVisible(true);
  }, []);

  const detailTitle = isOpenFixed ? '退换货运费保障-年框活动' : '退换货保障首重运费';
  const timeBlock =
    effectTime && expireTime
      ? getBlock(
        '有效期',
        `${dayjs(effectTime).format('YYYY.MM.DD')} - ${dayjs(expireTime).format('YYYY.MM.DD')}`,
        null,
        null,
        null,
      )
      : null;

  const priceBlock =
    detailInfo && status !== 'MERCHANT_QUITTING' && status !== 'MERCHANT_FROZEN'
      ? getPriceBlock({
        baseFeeAmount,
        isOpenFixed,
        fixedFeeAmount,
        discountFeeAmount,
        promotionFlag,
        promotionHalfOff,
        promotionTitle,
        marketIcon,
        marketTitle,
        handleQuestionClick,
        handleOriginalPriceQuestionClick,
      })
      : null;

  return (
    <div className={styles.detailCard}>
      <div className={styles.row}>
        <div className={styles.rowMain}>
          <div className={styles.title}>保障内容</div>
          <div className={styles.content}>{detailTitle}</div>
        </div>
      </div>
      {priceBlock}
      <div className={styles.row}>
        <div className={styles.rowMain}>
          <div className={styles.title}>扣费账户</div>
          <div className={styles.content}>
            <img src={AliPayIcon} className={styles.aliPayIcon} />
            <span className={styles.aliPayLoginId}>{alipayLoginId}</span>
          </div>
        </div>
      </div>
      {timeBlock}

      <BasePopup
        visible={pricePopupVisible}
        setVisible={setPricePopupVisible}
        title={popupInfo.title}
        desc={popupInfo.desc}
        descType={popupInfo.descType}
      />
    </div>
  );
};

export default DetailCard;
