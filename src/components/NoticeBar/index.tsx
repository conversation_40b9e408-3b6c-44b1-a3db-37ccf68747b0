import { NoticeBar as AntdNoticeBar } from 'antd-mobile';
import styles from './index.module.css';
import { ICON_MAP } from '@/lib/constant';

export interface NoticeBarOptions {
  content: string | null;
  iconType: 'alert' | 'error'|'info'|'default';
  colorType: 'alert' | 'error'|'info'|'default';
}

const NoticeBar = ({ content, iconType, colorType }: NoticeBarOptions) => {
  // 如果没有内容，不渲染NoticeBar
  if (!content) {
    return null;
  }

  return (
    <AntdNoticeBar
      content={<div className={styles.noticeBarContent}>{content}</div>}
      color={colorType}
      icon={<img src={ICON_MAP[iconType]} className={styles.noticeBarIcon} />}
      wrap
      className={styles.noticeBar}
    />
  );
};

export default NoticeBar;
