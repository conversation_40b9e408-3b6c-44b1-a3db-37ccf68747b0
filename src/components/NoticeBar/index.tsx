import { NoticeBar as AntdNoticeBar } from 'antd-mobile';
import styles from './index.module.css';
import { ICON_MAP } from '@/lib/constant';

export interface NoticeBarOptions {
  content: string;
  type: 'alert' | 'error';
}

const NoticeBar = ({ content, type }: NoticeBarOptions) => {
  return (
    <AntdNoticeBar
      content={<div className={styles.noticeBarContent}>{content}</div>}
      color={type}
      icon={<img src={ICON_MAP[type]} className={styles.noticeBarIcon} />}
      wrap
    />
  );
};

export default NoticeBar;
