import styles from './index.module.css';

const TITLE_IMG =
  'https://gw.alicdn.com/imgextra/i4/O1CN01cQZEwb1xMXNf9rF8c_!!6000000006429-2-tps-1500-316.png';
const HEX_IMG = 
  'https://gw.alicdn.com/imgextra/i3/O1CN01I67B5M1pM42rFlaSW_!!6000000005345-2-tps-542-593.png'
const ICON_LIST = [
  {
    icon: 'https://gw.alicdn.com/imgextra/i3/O1CN012ipZYt26rWeApuY1y_!!6000000007715-2-tps-84-84.png',
    title: '到店流量',
    subTitle: (
      <>
        <span>预估</span>
        <span className={styles.large}>+35%</span>
      </>
    ),
  },
  {
    icon: 'https://gw.alicdn.com/imgextra/i4/O1CN01NGiRK71RrcTsrfWKr_!!6000000002165-2-tps-84-84.png',
    title: '订单转化',
    subTitle: (
      <>
        <span>预估</span>
        <span className={styles.large}>+25%</span>
      </>
    ),
  },
  {
    icon: 'https://gw.alicdn.com/imgextra/i3/O1CN01TwJ1SC1DN1OmvLoNt_!!6000000000203-2-tps-84-84.png',
    title: '售后纠纷',
    subTitle: (
      <>
        <span>预估</span>
        <span className={styles.large}>-40%</span>
      </>
    ),
  },
    {
    icon: 'https://gw.alicdn.com/imgextra/i3/O1CN01TwJ1SC1DN1OmvLoNt_!!6000000000203-2-tps-84-84.png',
    title: '享',
    subTitle: (
      <>
        <span className={styles.large}>退货宝 专属标识</span>
      </>
    ),
  },
];

const Header = () => {
  return (
    <div className={styles.headerContainer}>
      <img className={styles.titleImg} src={TITLE_IMG} />
      <img className={styles.hex} src={HEX_IMG} />
      <div className={styles.iconList}>
        {ICON_LIST.map((item) => (
          <div className={styles.iconItem}>
            <div className={styles.iconTitle}>
              <img className={styles.icon} src={item.icon} />
              <div className={styles.title}>{item.title}</div>
            </div>
            <div className={styles.iconSubTitle}>{item.subTitle}</div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default Header;
