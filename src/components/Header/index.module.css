.headerContainer {
  background: linear-gradient(180deg, rgba(61, 94, 255, 0.4) 0%, rgba(61, 94, 255, 0.01) 100%);
  font-size: var(--text-size-base);
  padding: 40rpx 0rpx 0rpx 0rpx;
  position: relative;
  height:400rpx;
}


.titleImg {
  display: flex;
  width:100%;
  margin-top: 8rpx;
  padding-left:0rpx;
  
gap: 16rpx;
}
.hexImg{
  position: absolute;
  top: 70rpx;
  right: -60rpx;
  width: 276rpx;
  height: 301rpx;
  opacity: 0.5;
  z-index: 0;
}
.iconList {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  justify-content: center;
  align-items: center;
  margin:50rpx 32rpx 0rpx 32rpx;
  padding: 24rpx 32rpx 24rpx 32rpx;
  gap: 32rpx 40rpx;
border-radius: 12rpx 12rpx 0rpx 0rpx;
background: rgba(61, 94, 255, 0.06);
}

.iconItem {
  width: 276rpx;
  height: 42rpx;
  display: flex;
  align-items: center;
  padding: 0rpx;
  gap: 5rpx;

  flex-basis: calc(50% - 20rpx);
  justify-content: flex-start;
}
.iconTitle {
  display: flex;
  align-items: center;
  gap: 6rpx;
}

.icon {
  height: 42rpx;
}

.title {
  color: var(--text-secondary);
}

.iconSubTitle {
  margin-top: 8rpx;
  font-family: 'AlibabaFontMd';
  font-weight: 500;
  color: var(--text-base);
}

.large {
  font-size: 26rpx;
}
