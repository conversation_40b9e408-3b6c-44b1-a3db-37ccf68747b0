.headerContainer {
  background: linear-gradient(180deg, rgba(61, 94, 255, 0.11) 0%, rgba(61, 94, 255, 0.01) 100%);
  font-size: var(--text-size-base);
  padding-top: 30rpx;
  padding-bottom: 38rpx;
  padding-left: 32rpx;
  padding-right: 32rpx;
}

.subTitle {
  color: #6b7e90;
}

.titleImg {
  height: 58rpx;
  margin-top: 8rpx;
}

.iconList {
  margin-top: 16rpx;
  display: flex;
  gap: 48rpx;
}

.iconTitle {
  display: flex;
  align-items: center;
  gap: 6rpx;
}

.icon {
  height: 42rpx;
}

.title {
  color: var(--text-secondary);
}

.iconSubTitle {
  margin-top: 8rpx;
  font-family: 'AlibabaFontMd';
  font-weight: 500;
  color: var(--text-base);
}

.large {
  font-size: 36rpx;
  margin-left: 4rpx;
}
