.headerContainer {
  background: linear-gradient(180deg, rgba(61, 94, 255, 0.11) 0%, rgba(61, 94, 255, 0.01) 100%);
  font-size: var(--text-size-base);
  padding-top: 30rpx;
  padding-bottom: 38rpx;
  padding-left: 32rpx;
  padding-right: 32rpx;
}

.subTitle {
  color: #6b7e90;
}

.titleImg {
  width:90%;
  margin-top: 8rpx;
  padding: 0rpx 32rpx;
gap: 16rpx;
}

.iconList {
  display: flex;
  direction: row;
  flex-wrap: wrap;
  justify-content: space-between;
align-items: center;
padding: 24rpx 32rpx;
}

.iconTitle {
  display: flex;
  align-items: center;
  gap: 6rpx;
}

.icon {
  height: 42rpx;
}

.title {
  color: var(--text-secondary);
}

.iconSubTitle {
  margin-top: 8rpx;
  font-family: 'AlibabaFontMd';
  font-weight: 500;
  color: var(--text-base);
}

.large {
  font-size: 36rpx;
  margin-left: 4rpx;
}
