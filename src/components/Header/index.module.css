.headerContainer {
  background: linear-gradient(180deg, rgba(61, 94, 255, 0.11) 0%, rgba(61, 94, 255, 0.01) 100%);
  font-size: var(--text-size-base);
  padding-top: 30rpx;
  padding-bottom: 0rpx;
  padding-left: 32rpx;
  padding-right: 32rpx;
}

.subTitle {
  color: #6b7e90;
}

.titleImg {
  width:90%;
  margin-top: 8rpx;
  padding: 0rpx 32rpx;
gap: 16rpx;
}

.iconList {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  justify-content: center;
  align-items: center;
  padding: 24rpx 32rpx;
  gap: 32rpx 40rpx;
border-radius: 12rpx 12rpx 0rpx 0rpx;
background: rgba(61, 94, 255, 0.06);
}

.iconItem {
  width: 276rpx;
  height: 42rpx;
  display: flex;
  align-items: center;
  padding: 0rpx;
  gap: 8rpx;
  z-index: 0;
  flex-basis: calc(50% - 20rpx);
  justify-content: flex-start;
}
.iconTitle {
  display: flex;
  align-items: center;
  gap: 6rpx;
}

.icon {
  height: 42rpx;
}

.title {
  color: var(--text-secondary);
}

.iconSubTitle {
  margin-top: 8rpx;
  font-family: 'AlibabaFontMd';
  font-weight: 500;
  color: var(--text-base);
}

.large {
  font-size: 20rpx;
  margin-left: 4rpx;
}
