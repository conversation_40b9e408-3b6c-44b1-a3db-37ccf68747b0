.tabsContainer {
  background-color: #f5f5f5;
}

/* 自定义标签页头部 */
.tabHeader {
  display: flex;
  justify-content: flex-start; /* 左对齐 */
  align-items: center;
  background-color: #fff;
  padding: 16rpx 28rpx; /* 调整内边距，左右都有 */
  border-bottom: 1rpx solid #f0f0f0;
  gap: 32rpx; /* 增加间距，确保文字有足够空间 */
}

.tabItem {
  font-size: 30rpx; /* 30rpx转换为15px (30rpx ÷ 2 = 15px) */
  color: #111111;
  font-weight: 500;
  cursor: pointer;
  transition: color 0.15s ease, font-size 0.15s ease;
  user-select: none;
  padding: 4rpx 8rpx; /* 增加左右内边距，确保文字不被截断 */
  /* 添加触摸优化 */
  -webkit-tap-highlight-color: transparent;
  touch-action: manipulation;
  /* 确保文字完整显示 */
  white-space: nowrap;
  min-width: fit-content;
  flex-shrink: 0; /* 防止被压缩 */
}

.tabItem:hover {
  color: #666;
}

.tabItemActive {
  font-size: 36rpx !important;
  color: #111111 !important;
  font-weight: 500 !important;
}

/* 标签页内容区域 */
.tabContentWrapper {
  background-color: #f5f5f5;
  min-height: calc(100vh - 80rpx);
}
