.tabsContainer {
  background-color: #f5f5f5;
}

/* 自定义标签页头部 */
.tabHeader {
  display: flex;
  justify-content: flex-start; /* 左对齐 */
  background-color: #fff;
  align-items: center;
  padding: 0rpx 32rpx;
  gap: 32rpx;
}

.tabItem {
display: flex;
justify-content: center;
align-items: flex-end;
padding: 0rpx 0rpx 12rpx 0rpx;
gap: 8rpx;
}
.tabItemText{
  font-size: 30rpx;
  font-weight: 500;
  line-height: 44rpx;
  letter-spacing: normal;
  color: #111111;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  transform-origin: center;
}

.tabItemText.active {
  font-size: 36rpx;
  line-height: 52rpx;
  transform: scale(1.2);
  color: #111111;
}

/* 标签页内容区域 */
.tabContentWrapper {
  background-color: #f5f5f5;
}
