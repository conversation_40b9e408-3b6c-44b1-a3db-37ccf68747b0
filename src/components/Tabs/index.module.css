.tabsContainer {
  background-color: #f5f5f5;
}

/* 自定义标签页头部 */
.tabHeader {
  display: flex;
  justify-content: flex-start; /* 左对齐 */
  background-color: #fff;
  align-items: center;
  padding: 0rpx 32rpx;
  gap: 32rpx;
}

.tabItem {
display: flex;
justify-content: center;
align-items: flex-end;
padding: 0rpx 0rpx 12rpx 0rpx;
gap: 8rpx;
}
.tabItemText{
font-size: 30rpx;
font-weight: 500;
line-height: 44rpx;
letter-spacing: normal;
color:#111111
}
.tabItemActive {
  font-size: 36rpx !important;
  color: #111111 !important;
  font-weight: 500 !important;
}

/* 标签页内容区域 */
.tabContentWrapper {
  background-color: #f5f5f5;
}
