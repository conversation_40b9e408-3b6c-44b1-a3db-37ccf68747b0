.tabsContainer {
  background-color: #f5f5f5;
}

/* 自定义标签页头部 */
.tabHeader {
  display: flex;
  justify-content: flex-start; /* 左对齐 */
  align-items: center;
  background-color: #fff;
  padding: 16px; /* 调整内边距，左右都有 */
  border-bottom: 1px solid #f0f0f0;
  gap: 16px; /* 32rpx转换为16px (32rpx ÷ 2 = 16px) */
}

.tabItem {
  font-size: 15px; /* 30rpx转换为15px (30rpx ÷ 2 = 15px) */
  color:  #111111;
  font-weight: 500;
  cursor: pointer;
  transition: color 0.15s ease, font-size 0.15s ease;
  user-select: none;
  padding: 4px 0; /* 减少左右内边距 */
  /* 添加触摸优化 */
  -webkit-tap-highlight-color: transparent;
  touch-action: manipulation;
}

.tabItem:hover {
  color: #666;
}

.tabItemActive {
  font-size: 18px !important; /* 选中时稍微放大 (36rpx ÷ 2 = 18px) */
  color: #111111 !important;
  font-weight: 500 !important;
}

/* 标签页内容区域 */
.tabContentWrapper {
  background-color: #f5f5f5;
  min-height: calc(100vh - 80px);
}
