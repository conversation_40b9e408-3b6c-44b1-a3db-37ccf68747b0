import { useState, ReactNode } from 'react';
import styles from './index.module.css';

export interface TabItem {
  key: string;
  title: string;
}

export interface TabsProps {
  tabs: TabItem[];
  defaultActiveKey?: string;
  onChange: (activeKey: string) => void;
}

const Tabs = ({ 
  tabs, 
  defaultActiveKey, 
  onChange, 
}: TabsProps) => {
  const [activeKey, setActiveKey] = useState(defaultActiveKey || tabs[0]?.key || '');

  const handleTabClick = (key: string) => {
    setActiveKey(key);
    onChange(key);
  };

  return (
    <div className={styles.tabsContainer}>
      {/* 标签页头部 */}
      <div className={styles.tabHeader}>
        {tabs.map(tab => (
          <div
            key={tab.key}
            className={`${styles.tabItem} ${activeKey === tab.key ? styles.tabItemActive : ''}`}
            onClick={() => handleTabClick(tab.key)}
          >
            {tab.title}
          </div>
        ))}
      </div>
      
    </div>
  );
};

export default Tabs;
