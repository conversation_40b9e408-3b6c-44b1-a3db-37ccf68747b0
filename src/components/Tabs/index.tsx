import { useState, useCallback, memo } from 'react';
import styles from './index.module.css';

export interface TabItem {
  key: string;
  title: string;
}

export interface TabsProps {
  tabs: TabItem[];
  defaultActiveKey?: string;
  onChange: (activeKey: string) => void;
}

const Tabs = memo(({
  tabs,
  defaultActiveKey,
  onChange,
}: TabsProps) => {
  const [activeKey, setActiveKey] = useState(defaultActiveKey || tabs[0]?.key || '');

  const handleTabClick = useCallback((key: string) => {
    if (key === activeKey) return; // 避免重复点击
    setActiveKey(key);
    onChange(key);
  }, [activeKey, onChange]);

  return (
    <div className={styles.tabsContainer}>
      {/* 标签页头部 */}
      <div className={styles.tabHeader}>
        {tabs.map((tab) => (
          <div
            key={tab.key}
            className={styles.tabItem}
            onClick={() => handleTabClick(tab.key)}
          >
            <div className={`${styles.tabItemText} ${activeKey === tab.key ? styles.active : ''}`}>
              {tab.title}
            </div>
          </div>
        ))}
      </div>

    </div>
  );
});

export default Tabs;
