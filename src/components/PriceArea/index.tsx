import PriceCard from '../PriceCard';
import styles from './index.module.css';
import { useState, useEffect } from 'react';
import { RightOutlined } from '@ant-design/icons';
import FixedRules from '../Popup/fixedRules';
import { RenderInfo } from '@/types';
import dayjs from 'dayjs';

interface PriceAreaProps {
  renderInfo: RenderInfo;
  changeSelected: (selectedIndex: number) => void; // 选择价格回调，传入选中的数组索引
}

const PriceArea = ({
  renderInfo,
  changeSelected,
}: PriceAreaProps) => {
  // 获取默认选中的索引 - 选择第一个可访问的卡片
  const getDefaultSelection = (): number => {
    const firstAvailableIndex = renderInfo.findIndex((item) => item.isAccess);
    return firstAvailableIndex >= 0 ? firstAvailableIndex : 0;
  };

  const [selectedIndex, setSelectedIndex] = useState(getDefaultSelection());
  const [popupVisible, setPopupVisible] = useState(false);

  // 当 renderInfo 变化时，重新计算默认选中项
  useEffect(() => {
    const defaultIndex = getDefaultSelection();
    setSelectedIndex(defaultIndex);
  }, [renderInfo]);

  const handleCardSelect = (index: number) => {
    setSelectedIndex(index);
    changeSelected(index);
  };

  // 获取当前选中信息
  const selectedPriceInfo = renderInfo[selectedIndex];
  const isFixedPrice = selectedPriceInfo?.type === 'fixed';

  // 时间格式化工具
  const formatTime = (time?: number | null, format = 'YYYY-MM-DD'): string => {
    return time ? dayjs(time).format(format) : '--';
  };
  const getDetailTitle = (fixedPriceType?: string) => {
    switch (fixedPriceType) {
      case 'YEAR':
        return '年框一口价规则';
      case 'HALF_YEAR':
        return '半年框一口价规则';
      case 'SEASON':
        return '季框一口价规则';
      case 'MONTH':
        return '月框一口价规则';
      default:
        return '一口价规则';
    }
  };
  // 详情内容渲染
  const renderDetailContent = () => {
    if (!selectedPriceInfo) return null;

    if (isFixedPrice) {
      return (
        <div className={styles.fixedDetail}>
          <div className={styles.detailTitle}>{getDetailTitle(selectedPriceInfo.fixedPriceType)}</div>
          <div className={styles.expireTime}>
            {`有效期  ${formatTime(selectedPriceInfo.effectTime)}~${formatTime(selectedPriceInfo.expireTime)}`}
          </div>
          <div className={styles.detailText}>
            该活动是限时邀请制，只针对部分符合准入条件的商家开放，商家不能主动申请参与。
            <span
              className={styles.detailTips}
              onClick={() => setPopupVisible(true)}
            >
              规则详情<RightOutlined />
            </span>
          </div>
        </div>
      );
    }

    return (
      <div className={styles.detailText}>
        服务费将根据店铺交易订单退换货等情况动态调整，最终以实际出单收费为准。
      </div>
    );
  };

  return (
    <>
      <div className={styles.priceArea}>
        {/* ====== 价格卡片区域 ====== */}
        <div className={styles.priceCard}>
          {renderInfo.map((item, index) => {
            // 只根据 isAccess 判断是否渲染
            if (!item.isAccess) return null;
            return (
              <div key={index} className={styles.priceCardItem}>
                <PriceCard
                  priceInfo={item}
                  isSelected={selectedIndex === index}
                  onSelect={() => handleCardSelect(index)}
                />
              </div>
            );
          })}
        </div>

        {/* ====== 详情区域 ====== */}
        <div className={styles.detail}>
          {renderDetailContent()}
        </div>
      </div>

      {/* ====== 规则弹窗 ====== */}
      <FixedRules
        visible={popupVisible}
        onClose={() => setPopupVisible(false)}
        fixedPriceType={selectedPriceInfo?.fixedPriceType || ''}
      />
    </>
  );
};

export default PriceArea;
