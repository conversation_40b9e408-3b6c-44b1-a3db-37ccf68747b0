.priceArea {
  display: flex;
  flex-direction: column;
  gap: 15rpx;
}
.priceCard {
  display: flex;
  flex-wrap: wrap;
  gap: 15rpx;
}
.detail {
  display: flex;
  flex-direction: column;
  margin: auto;
  padding: 14rpx 18rpx;
  gap: 12rpx;
  border-radius: 12rpx;
  background: #f7f8fa;
}
.detailText {
  font-size: 24rpx;
  color: #666;
}
.priceCardItem {
  flex: 1;
}
.fixedDetail {
  display: flex;
  flex-direction: column;
  gap: 12rpx;
  max-height: 198rpx;
}
.detailTitle {
  font-size: 24rpx;
  color: #111;
}
.expireTime {
  font-size: 24rpx;
  color: #666;
}
.detailTips {
  display: inline-block;
  margin-left: 8px;
  color: #666;
  cursor: pointer;
}