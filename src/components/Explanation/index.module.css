.title {
  font-size: var(--text-size-base);
  font-weight: var(--text-weight-xl);
  margin-bottom: 24rpx;
}

.row {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.option {
  display: flex;
  align-items: center;
}

.iconContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.icon {
  width: 48rpx;
}

.text {
  font-size: 24rpx;
  margin-top: 12rpx;
}

.arrow {
  width: 32rpx;
}

.gap {
  display: flex;
  padding: 24rpx 44rpx;
}

.end {
  justify-content: end;
  padding: 24rpx 56rpx;
}
