import styles from './index.module.css';

const upDoorFlow = [
  {
    icon: 'https://gw.alicdn.com/imgextra/i3/O1CN01Md4mgr25B6LD8qR0c_!!6000000007487-2-tps-96-96.png',
    text: '申请退换货',
    iconClass: styles.returnIcon,
  },
  {
    icon: 'https://gw.alicdn.com/imgextra/i3/O1CN01gMleb51FVNrTicNoj_!!6000000000492-2-tps-96-96.png',
    text: '选择官方上门',
    iconClass: styles.doorIcon,
  },
  {
    icon: 'https://gw.alicdn.com/imgextra/i4/O1CN01jL1EDb1E8eadHDqOK_!!6000000000307-2-tps-96-96.png',
    text: '减免首重运费',
    iconClass: styles.truckIcon,
  },
];

const selfResendFlow = [
  {
    icon: 'https://gw.alicdn.com/imgextra/i3/O1CN01Md4mgr25B6LD8qR0c_!!6000000007487-2-tps-96-96.png',
    text: '申请退换货',
  },
  {
    icon: 'https://gw.alicdn.com/imgextra/i1/O1CN01GaR7S81t5oNT1E3Nt_!!6000000005851-2-tps-96-96.png',
    text: '选择自行寄回',
  },
  {
    icon: 'https://gw.alicdn.com/imgextra/i1/O1CN012XN5zZ1O9heeiye0D_!!6000000001663-2-tps-96-96.png',
    text: '填写物流单号',
  },
  {
    icon: 'https://gw.alicdn.com/imgextra/i4/O1CN01WS2ppw1GKgDoA2Hao_!!6000000000604-2-tps-96-96.png',
    text: '补偿金到账',
  },
  {
    icon: 'https://gw.alicdn.com/imgextra/i1/O1CN01Foqz8c1GMVnwuGCtD_!!6000000000608-2-tps-96-96.png',
    text: '退货运费保障',
  },
  {
    icon: 'https://gw.alicdn.com/imgextra/i2/O1CN01wmNASE1TuwaMEBBRW_!!6000000002443-2-tps-96-96.png',
    text: '商家确认收货',
  },
];

const rightIcon =
  'https://gw.alicdn.com/imgextra/i2/O1CN01mUgeYb1yx0SSeO694_!!6000000006644-2-tps-64-64.png';

const Explanation = () => {
  return (
    <div className={styles.container}>
      <div className={styles.flow}>
        <div className={styles.title}>上门取件</div>
        <div className={styles.row}>
          {upDoorFlow.map((item, index) => (
            <>
              <div className={styles.option} key={index}>
                <div className={`${styles.iconContainer}`}>
                  <img src={item.icon} className={styles.icon} />
                  <div className={styles.text}>{item.text}</div>
                </div>
              </div>
              {index < upDoorFlow.length - 1 && <img src={rightIcon} className={styles.arrow} />}
            </>
          ))}
        </div>
      </div>

      <div className={styles.flow} style={{ marginTop: '48rpx' }}>
        <div className={styles.title}>自行寄回</div>
        <div className={styles.row}>
          {selfResendFlow.slice(0, 3).map((item, index) => (
            <>
              <div className={styles.option} key={index}>
                <div className={`${styles.iconContainer}`}>
                  <img src={item.icon} className={styles.icon} />
                  <div className={styles.text}>{item.text}</div>
                </div>
              </div>
              {index < upDoorFlow.length - 1 && <img src={rightIcon} className={styles.arrow} />}
            </>
          ))}
        </div>
        <div className={`${styles.gap} ${styles.end}`}>
          <img src={rightIcon} className={styles.arrow} style={{ rotate: '90deg' }} />
        </div>
        <div className={styles.row}>
          {selfResendFlow.slice(3, 6).map((item, index) => (
            <>
              <div className={styles.option} key={index}>
                <div className={`${styles.iconContainer}`}>
                  <img src={item.icon} className={styles.icon} />
                  <div className={styles.text}>{item.text}</div>
                </div>
              </div>
              {index < upDoorFlow.length - 1 && (
                <img src={rightIcon} className={styles.arrow} style={{ rotate: '180deg' }} />
              )}
            </>
          ))}
        </div>
      </div>
    </div>
  );
};

export default Explanation;
