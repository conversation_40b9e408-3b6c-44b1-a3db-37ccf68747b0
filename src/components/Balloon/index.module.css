/* 自定义气泡提示样式 */
.balloon {
  position: absolute;
  top: 220rpx;
  left: 50%;
  transform: translateX(-50%);
  z-index: 1000;
}

.balloonContent {
  background: #3d5eff;
  color: white;
  padding: 16rpx 20rpx;
  border-radius: 12rpx;
  font-size: 24rpx;
  line-height: 32rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  white-space: nowrap;
  min-width: 200rpx;
}

.balloonText {
  margin-right: 16rpx;
}

.balloonClose {
  width: 24rpx;
  height: 24rpx;
  cursor: pointer;
  flex-shrink: 0;
}

.balloonArrow {
  position: absolute;
  bottom: -8rpx;
  left: 50%;
  transform: translateX(-50%) rotate(45deg);
  width: 16rpx;
  height: 16rpx;
  border-right: 3rpx solid;
  border-bottom: 3rpx solid;
  z-index: 2;
  background: #3d5eff;
  border-color: #3d5eff;
} 