import { useCallback } from 'react';
import { CloseOutline } from 'antd-mobile-icons';
import styles from './index.module.css';

interface BalloonProps {
  visible: boolean;
  setVisible: (visible: boolean) => void;
  desc: string;
}

const Balloon = ({ visible, setVisible, desc }: BalloonProps) => {
  const handleClose = useCallback(() => {
    setVisible(false);
  }, [setVisible]);

  if (!visible) return null;

  return (
    <div className={styles.balloon}>
      <div className={styles.balloonContent}>
        <span className={styles.balloonText}>{desc}</span>
        <CloseOutline className={styles.balloonClose} onClick={handleClose} />
        <div className={styles.balloonArrow} />
      </div>
    </div>
  );
};

export default Balloon;
